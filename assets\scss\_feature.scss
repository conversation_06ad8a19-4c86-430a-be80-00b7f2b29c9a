.feature-wrapper {
    @include flex;
    justify-content: space-between;
    border: 1px dashed #C8CBCD;
    padding: 50px 40px;

    @include breakpoint (max-lg){
        flex-wrap: wrap;
        gap: 25px;
    }

    .feature-items {
        @include flex;
        gap: 20px;

        .content {
            h6 {
                font-weight: 600;
            }

            p {
                font-size: 15px;
            }
        }
    }
}

.printing-feature-wrapper {
    .printing-image {
        img {
            @include imgw;
        }
    }

    .printing-items {
        text-align: center;

        .icon {
            width: 110px;
            height: 110px;
            line-height: 100px;
            text-align: center;
            border-radius: 50%;
            background-color: $white;
            margin: 0 auto;
        }

        .content {
            margin-top: 20px;

            h3 {
                font-weight: 600;
                margin-bottom: 8px;
                font-size: 20px;
            }
        }
    }
}

.choose-us-wrapper {
    .choose-us-image {
        img {
            @include imgw;
        }
    }

    .choose-us-content {
        margin-left: 30px;
        
        .icon-items {
            margin-top: 30px;
            @include flex;
            gap: 15px;
        }

        .theme-btn {
            margin-top: 40px;
        }
    }
}

.work-process-card-items {
    margin-top: 30px;
    text-align: center;

    .icon {
        width: 100px;
        height: 100px;
        line-height: 100px;
        text-align: center;
        border-radius: 50%;
        background: linear-gradient(90deg, rgba(73, 81, 254, 0.112) 24%, rgba(73, 81, 254, 0.112) 38%, rgba(148, 69, 211, 0.112) 62%, rgba(223, 57, 167, 0.112) 100%);
        margin: 0 auto;
        position: relative;
    }

    .content {
        margin-top: 20px;

        h3 {
            margin-bottom: 8px;
        }
    }
}

.counter-box-items {
    text-align: center;

    .icon {
        width: 120px;
        height: 120px;
        line-height: 120px;
        border-radius: 50%;
        text-align: center;
        margin: 0 auto;
        border: 1px dashed $white;
        filter: grayscale(100%) brightness(300%);

        img {
            filter: grayscale(100%) brightness(300%);
        }
    }

    .content {
        margin-top: 20px;

        h2 {
            color: $white;
            font-size: 60px;
        }

        p {
            font-size: 18px;
            color: $white;
            margin-top: 5px;
        }
    }
}

.how-works-content {
    h2 {
        font-size: 40px;
        margin-top: 30px;
        margin-bottom: 10px;
    }

    p {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 40px;
    }
}

.how-work-items {
    text-align: center;
    background-color: $white;
    padding: 30px 30px;
    box-shadow: $shadow;

    .thumb {

        img {
            height: 180px;
            object-fit: cover;
        }
    }

    .content {
        margin-top: 20px;

        h4 {
            margin-bottom: 10px;
        }
    }
}

.good-quality-wrapper {
    .quality-image {
        img {
            @include imgw;
        }
    }

    .good-quality-content{
        br {
            @include breakpoint (max-xl){
                display: none;
            }
        }
        
        .list-item {
            margin-top: 30px;
            @include flex;
            gap: 80px;
            margin-bottom: 50px;

            @include breakpoint (max-xl){
                flex-wrap: wrap;
                gap: 20px;
            }

            ul {
                li {
                    font-weight: 500;
                    color: $header-color;

                    i {
                        margin-right: 5px;
                        color: $theme-color;
                    }

                    &:not(:last-child){
                        margin-bottom: 12px;
                    }
                }
            }
        }
    }
}

.custom-order-wrapper-news {
    .custom-image {
        position: relative;

        @include breakpoint (max-lg){
            display: none;
        }

        .premium-logo {
            position: absolute;
            top: 50px;
            left: 0;

            @include breakpoint (max-xl){
                display: none;
            }
        }
    }
    .icon-box-area {
        background-color: $theme-color;
        padding: 40px;
        margin-left: -250px;
        position: relative;
        z-index: 9;
        margin-top: 40px;

        @include breakpoint (max-lg){
           margin-left: 0;
        }

        .icon-items {
            display: flex;
            gap: 15px;

            .icon {
                width: 55px;
                height: 55px;
                line-height: 55px;
                background-color: $white;
                text-align: center;
            }

            .content {
                flex-basis: 80%;

                h3 {
                    font-size: 18px;
                    font-weight: 600;
                    color: $white;
                    margin-bottom: 10px;
                }

                p {
                    color: rgba(255, 255, 255, 0.85)
                }        
            }
        }
    }
}