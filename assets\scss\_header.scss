.header-top-section {
	background: linear-gradient(90.07deg, #FF4E8D 32.43%, #AE34E8 58.79%, #3E8DFF 105.32%);
	position: relative;
	z-index: 9;

	@include breakpoint (max-lg){
		display: none;
	}

	.container-fluid {
		padding: 0 250px;

		@include breakpoint (max-xxxl){
			padding: 0 40px;
		}

		@include breakpoint (max-xl){
			padding: 0 30px;
		}
	}
}

.header-top-wrapper {
	@include flex;
	justify-content: space-between;
	position: relative;
	z-index: 99;

	.contact-list {
		@include flex;
		gap: 40px;

		li {
			color: $white;
			font-weight: 400;

			a {
				color: $white;
			}

			i {
				color: $white;
				margin-right: 10px;
			}
		}
	}

	p {
		font-size: 18px;
		font-weight: 500;
		color: $white;
		padding: 10px 0;
	}

	.header-top-right {
		@include flex;
		gap: 20px;

		.social-icon {
			gap: 20px;

			a {
				color: $white;
			}
		}

		.nice-items {
			margin-right: 15px;

			.nice-select {
				padding: 10px 7px 10px 20px;
				background: transparent;
				border: none;
				text-align: center;
				margin: 0 auto;
				position: relative;
				z-index: 999;
	
				span {
					font-size: 16px;
					font-weight: 700;
					text-transform: uppercase;
					color: $white;
				}
	
				&::after {
					right: -10px;
					top: 24px;
					border-bottom: 1px solid $white;
					border-right: 1px solid $white;
				}
	
				.list {
					background-color: $bg-color;
					border-radius: 0;
					right: initial;
					font-size: 14px;
					//padding: 5px 10px;
					margin-top: 0;

					li {
						font-weight: 500;
					}
				}
	
				.option {
					border: none;
				}
			}
		}
	}
}

.header-section-2 {
	position: relative;
	z-index: 9999;
	background-color: $white;
    .container-fluid {
        padding: 0 150px;

		@include breakpoint (max-xl4){
			padding: 0 50px;
		}

		@include breakpoint (max-xxxl){
			padding: 0 40px;
		}

		@include breakpoint (max-xl){
			padding: 0 20px;
		}
    }
}

//>>>>> Header Main Area Start <<<<<//
.header-section-1 {
	background-color: $white;
	position: relative;
	z-index: 9999;
}

.header-section {
	position: relative;
	z-index: 9999;
	background-color: $white;
}

.header-1 {

	.container-fluid {
		padding: 0 250px;

		@include breakpoint (max-xxxl){
			padding: 0 40px;
		}

		@include breakpoint (max-xl){
			padding: 0 30px;
		}
	}

	.header-right {
		gap: 30px;

		@include breakpoint (max-sm){
			gap: 18px;
		}

		.search-icon {
			color: $header-color;
			font-size: 18px;
		}

		.user-icon {
			color: $header-color;
			font-size: 18px;
		}

		.menu-cart {
			position: relative;

			.cart-icon {
				position: relative;
				display: inline-block;
				border-radius: 50%;

				&::before {
					position: absolute;
					top: -7px;
					right: -12px;
					content: "3";
					width: 18px;
					line-height: 18px;
					height: 18px;
					border-radius: 18px;
					background-color: $theme-color;
					color: $white;
					font-size: 12px;
					text-align: center;
					font-weight: 500;
				}
				i {
					color: $header-color;
					font-size: 18px;
				}
			}

			&:hover {
				.cart-box {
					transform: rotateX(0deg);
					visibility: visible;
				}
			}
		}
	}

	&.style-3 {
		.header-main {
			
			.main-menu {
				ul {
					li {
						a {
							color: $white;
						}

						.submenu {
							color: $header-color;
			
							li {
								
				
								a {
									color: $header-color;
								}
								
								&:hover {
									>a {
										background: $theme-color;
										color: $white !important;
			
										&::after {
											color: $theme-color;
										}
									}
									>.submenu {
										-webkit-transform: translateY(1);
										-moz-transform: translateY(1);
										-ms-transform: translateY(1);
										-o-transform: translateY(1);
										transform: translateY(1);
										visibility: visible;
										opacity: 1;
									}
								}
							}
							li.has-dropdown {
								>a {
									&::after {
										position: absolute;
										top: 50%;
										inset-inline-end: 25px;
										-webkit-transform: translateY(-50%);
										-moz-transform: translateY(-50%);
										-ms-transform: translateY(-50%);
										-o-transform: translateY(-50%);
										transform: translateY(-50%);
										color: $theme-color;
									}
								}
							}
						}
						
						&:hover {
							>a {
								color:  $theme-color;
								&::after {
									color:  $theme-color;
								}
							}
							>.submenu {
								visibility: visible;
								opacity: 1;
								transform: translateY(0px);
							}
						  }
					}
				}
			}
		}

		.header-logo {
			display: none;
		}

		.header-right {
			.search-icon {
				color: $white;
			}
	
			.user-icon {
				color: $white;
			}
	
			.menu-cart {
	
				.cart-icon {
					i {
						color: $white;
					}
				}
			}
		}

		.sidebar__toggle {
			color: $white;
		}
	}
}

.middle-section {
	position: relative;
	z-index: 9;
	border-bottom: 1px solid #d9dadc;
	background-color: $white;

	.container-fluid {
		padding: 0 250px;

		@include breakpoint (max-xxxl){
			padding: 0 40px;
		}

		@include breakpoint (max-xl){
			padding: 0 30px;
		}
	}

	@include breakpoint (max-xl){
		display: none;
	}
}

.main-header-wrapper {
    @include flex;
    gap: 100px;

	@include breakpoint (max-xl4){
		gap: 70px;
	}

	@include breakpoint (max-xxxl){
		gap: 40px;
	}

	@include breakpoint (max-xl){
		padding: 15px 0;
	}

    .main-header-items {
        width: 100%;

        .header-contact-info-area {
            @include flex;
            justify-content: space-between;
            padding: 0 50px;
            border: 1px solid #D4DCED;

			@include breakpoint (max-xxxl){
				padding: 0 30px;
			}

			@include breakpoint (max-xl){
				display: none;
			}
        
        
            @include breakpoint (max-sm){
                gap: 30px;
            }
        
            .contact-info-items {
                @include flex;
                gap: 20px;
                justify-content: space-between;
                position: relative;
                padding: 30px 0;
                border-right: 1px solid #D4DCED;
                padding-right: 50px;

				@include breakpoint (max-xxl){
					border-right: none;
					padding-right: 0;
				}
        
                .icon {
                    width: 40px;
                    height: 40px;
                    line-height: 40px;
                    text-align: center;
                    position: relative;
                    background-color:  $white;
                    border-radius: 50%;
                    color: $theme-color;
                    box-shadow: (0px 4px 25px rgba(0, 0, 0, 0.06));
                    position: relative;

					@include breakpoint (max-xxl){
						width: 30px;
						height: 30px;
						line-height: 30px;
					}

                    &::before {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        width: 64px;
                        height: 64px;
                        content: "";
                        background-color: transparent;
                        border-radius: 50%;
                        border: 1px solid rgba(30, 32, 35, 0.10);
                        transform: translate(-50%,-50%);

						@include breakpoint (max-xxl){
							width: 54px;
							height: 54px;
						}
                    }
                }
        
                .content {
                    p {
                        color: $text-color;
                        margin-bottom: 5px;
                        text-transform: capitalize;
                        font-size: 14px;
                        font-weight: 500;
                        opacity: 0.7;
                    }
        
                    h3 {
                        font-size: 20px;
                        color: $header-color;
                        text-transform: initial;

						@include breakpoint (max-xxxl){
							font-size: 18px;
						}
        
                        a {
                            color: $header-color;
                        }
                    }
                }

                &.style-2 {
                    border: none;
                    padding-right: 0;
                }
            }

			.header-button {
				.theme-btn {
					@include breakpoint (max-xxxl){
						font-size: 14px;
						padding: 24px 27px;
					}
				}
			}
        }
    }
}

.header-2 {
    .logo {
        display: none;
    }

	.header-main {
		
		.main-menu {
			ul {
				li {

					@include breakpoint (max-xxl){
						margin-inline-end: 40px;
					}
					
					.has-homemenu {
						@include breakpoint (max-xl4){
							left: -50px;
						}
					}
				}
			}
		}
	}

	.header-right {
		gap: 30px;

		@include breakpoint (max-sm){
			gap: 18px;
		}

		.search-icon {
			color: $header-color;
			font-size: 18px;
		}

		.user-icon {
			color: $header-color;
			font-size: 18px;
		}

		.menu-cart {
			position: relative;

			.cart-icon {
				position: relative;
				display: inline-block;
				border-radius: 50%;

				&::before {
					position: absolute;
					top: -7px;
					right: -12px;
					content: "3";
					width: 18px;
					line-height: 18px;
					height: 18px;
					border-radius: 18px;
					background-color: $theme-color;
					color: $white;
					font-size: 12px;
					text-align: center;
					font-weight: 500;
				}
				i {
					color: $header-color;
					font-size: 18px;
				}
			}

			&:hover {
				.cart-box {
					transform: rotateX(0deg);
					visibility: visible;
				}
			}
		}
	}
}

.header-section-3 {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 999;
}

.menu-thumb {
	@include breakpoint (max-lg){
		display: none !important;
	}
}

.header-main {
	display: flex;
	align-items: center;
	justify-content: space-between;

    @media (max-width: 1199px) {
        padding: 15px 0px;
    }
	
	.main-menu {
		ul {
			margin-bottom: 0;
			li {
				position: relative;
				list-style: none;
				display: inline-block;
				margin-inline-end: 50px;

				@include breakpoint (max-xxl){
					margin-inline-end: 40px;
				}
	
			&:last-child {
				margin-inline-end: 0;
			}
	
			a {
				display: inline-block;
				font-size: 16px;
				font-weight: 600;
				color:  $header-color;
				padding: 35px 0;
				text-align: left;
				position: relative;
				text-transform: capitalize;
				@include transition;

	
				i {
					font-size: 15px;
					font-weight: 600;
					margin-left: 5px;
				}
	
				&:hover {
					color: $theme-color !important;
				}
	
			}
			.submenu {
				position: absolute;
				top: 100%;
				inset-inline-start: 0;
				min-width: 240px;
				background: $white;
				z-index: 99999;
				visibility: hidden;
				opacity: 0;
				transform-origin: top center;
				color: $header-color;
				box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.2);
				-webkit-box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.2);
				-moz-box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.2);
				transform: translateY(10px);
				@include transition;

				li {
					display: block;
					width: 100%;
					margin: 0;
					padding: 0;
	
					a {
						position: relative;
						z-index: 11;
						font-size: 16px;
						font-weight: 600;
						color: $header-color;
						padding: 0 25px;
						padding-bottom: 11px;
						padding-top: 11px;
						width: 100%;
						border-bottom: 1px solid #eeeeee;
					}
					&:last-child {
						a {
							border: none;
						}
					}
					.submenu {
						inset-inline-start: 100%;
						top: 0;
						visibility: hidden;
						opacity: 0;
					}
					&:hover {
						>a {
							background: $theme-color;
							color: $white !important;

							&::after {
								color: $theme-color;
							}
						}
						>.submenu {
							-webkit-transform: translateY(1);
							-moz-transform: translateY(1);
							-ms-transform: translateY(1);
							-o-transform: translateY(1);
							transform: translateY(1);
							visibility: visible;
							opacity: 1;
						}
					}
				}
				li.has-dropdown {
					>a {
						&::after {
							position: absolute;
							top: 50%;
							inset-inline-end: 25px;
							-webkit-transform: translateY(-50%);
							-moz-transform: translateY(-50%);
							-ms-transform: translateY(-50%);
							-o-transform: translateY(-50%);
							transform: translateY(-50%);
							color: $theme-color;
						}
					}
				}
			}

			.has-home-menu {
				width: 1000px;
				padding: 30px 30px 10px 30px;
				opacity: 0;
				left: -250px;
				visibility: hidden;
				padding: 30px 30px 10px 30px;

				.home-menu {
					position: relative;

					.home-menu-thumb {
						position: relative;

						.demo-button {
							position: absolute;
							top: 50%;
							left: 50%;
							transform: translate(-50%, -50%);
							width: 70%;
							gap: 10px;
							display: flex;
							justify-content: center;
							flex-direction: column;
							opacity: 0;
							visibility: hidden;
							@include transition;
							margin-top: 20px;

							.theme-btn {
								padding: 14px 20px;
								color: $white !important;
								width: initial;
								font-size: 14px;
								text-align: center;
                                border-radius: 0px !important;

								&:hover {
									color: $white !important;
                                    
                                    &::before, &::after {
                                        border-radius: 0px;
                                    }
								}
							}
						}

						&::before {
							background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, $theme-color 100%);
							background-repeat: no-repeat;
							background-size: cover;
							background-position: center;
							width: 100%;
							height: 100%;
							position: absolute;
							left: 0;
							top: 0;
							overflow: hidden;
							opacity: 0;
							transition: all 0.3s ease-in-out;
							content: "";
						}
						&:hover{

							&::before {
								visibility: visible;
								opacity: 1;
							}

							.demo-button {
								opacity: 1;
								visibility: visible;
								margin-top: 0;
							}

							&.home-menu-btn {
								opacity: 1;
								visibility: visible;
								bottom: 50%;
								transform: translateY(50%);
							}
						}
						img {
							width: 100%;
						}
					}

					.home-menu-title {
						text-align: center;
						margin: 15px auto;
						display: inline-block;
						font-size: 16px;
					}
				}
			}
			
			&:hover {
				>a {
					color:  $theme-color;
					&::after {
						color:  $theme-color;
					}
				}
				>.submenu {
					visibility: visible;
					opacity: 1;
					transform: translateY(0px);
				}
			  }
			}
		}
	}
}

.sidebar__toggle {
	cursor: pointer;
}


.header-middle-wrapper {
	@include flex;
	justify-content: space-between;
	padding: 15px 0;

	.search-toggle-box {
		width: 300px;

		.input-area {
			position: relative;

			input {
				outline: none;
				border: 1px solid #d9dadc;
				background-color: $white;
				padding: 12px 20px;
				width: 100%;
				border-radius: 30px;
				color: $text-color;
			}

			.cmn-btn {
				position: absolute;
				top: 16px;
				right: 20px;
				font-size: 16px;
				color: $header-color;
			}
		}
	}

	.header-right {
		gap: 20px;

		.menu-cart {
			position: relative;

			.cart-icon {
				position: relative;
				display: inline-block;
				border-radius: 50%;

				&::before {
					position: absolute;
					top: -7px;
					right: -12px;
					content: "1";
					width: 18px;
					line-height: 18px;
					height: 18px;
					border-radius: 18px;
					background-color: $theme-color;
					color: $white;
					font-size: 12px;
					text-align: center;
					font-weight: 500;
				}
				i {
					color: $header-color;
					font-size: 18px;
				}
			}

			&:hover {
				.cart-box {
					transform: rotateX(0deg);
					visibility: visible;
				}
			}
		}
	}
}

.header-4 {
	position: relative;
	z-index: 9;
	background-color: $white;

	.header-main {
		justify-content: center;

		@include breakpoint (max-xl){
			justify-content: space-between;
		}
	}

	.header-logo {
		display: none;

		@include breakpoint (max-xl){
			display: block;
		}
	}
}

//>>>>> Sticky Start <<<<</
.sticky {
	position: fixed !important;
	top: 0 !important;
	left: 0;
	width: 100%;
	z-index: 999;
	background-color: $white;
	box-shadow: $shadow;
	transition: all 0.9s;
	-webkit-animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;
	animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;

	&.header-1 {
		&.style-3 {
			.header-main {
				
				.main-menu {
					ul {
						li {
							a {
								
								color: $header-color;
							}
						}
					}
				}

				.sidebar__toggle {
					color: $header-color;
				}
			}
	
			.header-logo-2 {
				display: none;
			}

			.header-logo {
				display: block;
			}
	
			.header-right {
				.search-icon {
					color: $header-color;
				}
		
				.user-icon {
					color: $header-color;
				}
		
				.menu-cart {
		
					.cart-icon {
						i {
							color: $header-color;
						}
					}
				}
			}
		}
	}

	&.header-2 {
        padding: 0 40px;

        .logo {
            display: block;
        }
    }

	&.header-4 {
		.header-main {
			justify-content: space-between;
		}
	
		.header-logo {
			display: block;
		}
	}	
}

//>>>>> Offcanvas Start <<<<<//
.offcanvas__info {
	background: $white none repeat scroll 0 0;
	border-left: 2px solid  $theme-color;
	position: fixed;
	right: 0;
	top: 0;
	width: 400px;
	height: 100%;
	-webkit-transform: translateX(calc(100% + 80px));
	-moz-transform: translateX(calc(100% + 80px));
	-ms-transform: translateX(calc(100% + 80px));
	-o-transform: translateX(calc(100% + 80px));
	transform: translateX(calc(100% + 80px));
	-webkit-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
	-moz-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
	transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
	z-index: 9999;
	overflow-y: scroll;
	overscroll-behavior-y: contain;
	scrollbar-width: none;
	&::-webkit-scrollbar {
		display: none;
	}
}

.offcanvas__info.info-open {
	opacity: 1;
	-webkit-transform: translateX(0);
	-moz-transform: translateX(0);
	-ms-transform: translateX(0);
	-o-transform: translateX(0);
	transform: translateX(0);
}


.offcanvas__wrapper {
	position: relative;
	height: 100%;
	padding: 30px 30px;

	.offcanvas__content {
		.text {
			color: $text-color;
		}

		.offcanvas__close {
			width: 45px;
			height: 45px;
			line-height: 45px;
			text-align: center;
			border-radius: 50%;
			background-color: $theme-color;
			position: relative;
			z-index: 9;
			cursor: pointer;

			i {
				color: $white;
			}
		}

		.offcanvas__contact {
			margin-top: 50px;

			ul {
				margin-top: 20px;

				li {
					font-size: 16px;
					font-weight: 500;
					text-transform: capitalize;

					&:not(:last-child){
						margin-bottom: 15px;
					}

					.offcanvas__contact-icon {
						margin-right: 20px;
						i {
							color: $theme-color;
						}
					}
				}
			}

			.header-button {
				.theme-btn {
					width: 100%;
					padding: 20px 40px;
				}
			}

			.social-icon {
                margin-top: 30px;
                gap: 10px;

                a {
                    width: 45px;
                    height: 45px;
                    line-height: 45px;
                    text-align: center;
                    font-size: 16px;
                    display: block;
                    background: transparent;
                    color: $header-color;
                    border-radius: 50%;
                    -webkit-transition: all .4s ease-in-out;
                    transition: all .4s ease-in-out;
                    text-align: center;
                    border: 1px solid $border-color;

                    &:hover {
                        background-color: $theme-color;
                        color: $white;
                    }
                }
            }
		}
	}
}

.offcanvas__overlay {
	position: fixed;
	height: 100%;
	width: 100%;
	background: #151515;
	z-index: 900;
	top: 0;
	opacity: 0;
	visibility: hidden;
	right: 0;
}

.offcanvas__overlay.overlay-open {
	opacity: 0.8;
	visibility: visible;
}

@media (max-width:450px) {
	.offcanvas__info {
		width: 300px;
	}
}

@media (max-width: 575px) {
	.offcanvas__wrapper {
		padding: 20px;
	}
}


//>>>>> Breadcrumb Start <<<<<//
.breadcrumb-wrapper{
	position: relative;
	overflow: hidden;
	z-index: 9;

	.page-heading{
		position: relative;
		z-index: 99;
		margin-top: -10px;

		h1 {
			font-size: 85px;
			position: relative;
			z-index: 9;
			line-height: 1;
			text-transform: capitalize;

			@include breakpoint(max-xl){
				font-size: 65px;
			}

			@include breakpoint(max-md){
				font-size: 45px;
			}
		}

		.breadcrumb-items{
			display: inline-flex;
            align-items: center;
			justify-content: center;
			gap: 10px;
            background-color: $white;
            padding: 8px 30px;
            border-radius: 30px;
            margin-top: 30px;
			
			li {
				font-size: 18px;
				font-weight: 500;
				text-transform: capitalize;
                background: var(--color-gradient-1);
                display: inline-block;
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;

				@include breakpoint (max-sm){
					font-size: 16px;
				}
	
				a {
					color: $theme-color;				;
					@include transition;
	
					&:hover{
						color: $theme-color;
					}
				}
	
				i{
					font-size: 18px;
					color: $theme-color;

					@include breakpoint (max-sm){
						font-size: 16px;
					}
				}
			}
		}
	}
}

.error-content {
	margin-top: -50px;
	
	h2 {
		font-weight: 700;
		font-size: 320px;
		color: $theme-color;
		line-height: 1;

		span {
			color: $header-color;
		}

		@include breakpoint (max-xl){
			font-size: 300px;
		}

		@include breakpoint (max-lg){
			font-size: 200px;
		}

		@include breakpoint (max-sm){
			font-size: 110px;
		}
	}

	h3 {
		font-weight: 700;
		font-size: 32px;
	}
}

//>>>>> Sidebar Area Start <<<<<//
.side_bar {
	position: fixed;
	top: 0;
	right: 0px;
	width: 420px;
	height: 100%;
	background-color: $white;
	padding: 40px;
	padding-top: 25px;
	transition: all 0.3s ease-in-out;
	box-shadow: $shadow;
	z-index: 9999;
    overflow-y: scroll;

	@include breakpoint (max-xxs){
		width: 350px;
	}
   
	
	.info {
		.icon__item{
		display: flex;
		gap: 20px;
		align-items: center;

		&:not(:last-child){
			margin-bottom: 30px;
		}
		.icon{
			color: $theme-color;
			font-size: 32px;
		}
		.content{
			p{
			margin-bottom: 5px;
			}
			h6{
			font-size: 16px;
			color: $white;
			}
		}
		}
	}

	.x-mark-icon {
		position: absolute;
		right: 40px;
		top: 25px;
		text-align: center;
		font-size: 20px;
		transition: all 0.3s ease-in-out;
		color: $header-color;
		
		&:hover {
			transform: rotate(90deg);
		}
	}

	.cartmini__del {
		position: absolute;
		top: 10px;
		right: 10px;
		width: 25px;
		height: 25px;
		line-height: 25px;
		text-align: center;
		color: $header-color;
		font-size: 16px;
	}

	p {
	  color: $white;
	}

	.cart-title {
		margin-bottom: 20px;

		h4 {
			color: $header-color;
		}
	}

	.cartmini__widget {
		height: 100%;

		.cartmini__widget-item {
			position: relative;
			display: flex;
			padding: 20px;
			border-bottom: 1px solid rgba(129, 129, 129, 0.2);
			transition: background-color 0.3s;
			gap: 30px;
			padding-left: 0;
			padding-top: 20px;
			align-items: center;

			.cartmini__thumb {
				max-width: 100px;
				height: 100px;

				img {
					@include imgw;
				}
			}

			.cartmini__content {
				h5 {
					font-size: 16px;
					font-weight: 600;
					margin-bottom: 5px;
				}
			}
		}
	}

	.cartmini__checkout {
		padding: 20px;
		padding-bottom: 85px;
		width: 100%;
		background: $white;
		border-top: 2px solid $border-color;
		margin-top: 150px;
		padding-right: 0;
		padding-left: 0;

		.cartmini__checkout-title {
			@include flex;
			justify-content: space-between;

			h4 {
				font-size: 18px;
				display: inline-block;
				font-weight: 600;
				margin-bottom: 0;
			}

			 span {
				font-size: 20px;
				font-weight: 600;
				color: $theme-color;
			}
		}
	}

	.cartmini__checkout-btn {
		.theme-btn {
			text-align: center;
			padding: 20px 60px;

			&.style-2 {
				background-color: $header-color;

				&::before,&::after {
					background-color: $theme-color;
				}
			}
		}
	}

  }
  .side_bar_hidden {
	visibility: hidden;
	opacity: 0;
	right: -30px;
  }
  //>>>>> Sidebar Area End <<<<<//