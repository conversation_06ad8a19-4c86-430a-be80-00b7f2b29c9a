.news-box-items {
    margin-top: 30px;
    background-color: #F5F5F5;
    @include transition;
    overflow: hidden;

    .news-image {
        position: relative;
        z-index: 2;
        overflow: hidden;

        img {
            @include imgw;
            position: relative;
            display: block;
            object-fit: cover;
            -webkit-transition: all 500ms ease;
            transition: all 500ms ease;

            &:first-child {
                position: absolute;
                left: 0;
                top: 0;
                right: 0;
                bottom: 0;
                z-index: 1;
                -webkit-transform: translateX(50%) scaleX(2);
                transform: translateX(50%) scaleX(2);
                opacity: 0;
                -webkit-filter: blur(10px);
                filter: blur(10px)
            }
        }
    }

    .news-content {
        padding: 20px 30px;

        .post-list {
            @include flex;
            gap: 95px;
            margin-bottom: 15px;

            li {
                @include transition;
            }
        }

        h4 {
            margin-bottom: 30px;
            font-size: 24px;
        }

        .author-items {
            @include flex;
            justify-content: space-between;
            border-top: 1px solid #d1d3e0;
            padding-top: 30px;
            @include transition;

            .author-info {
                @include flex;
                gap: 14px;

                .content {
                    h6 {
                        font-size: 16px;
                    }

                    p {
                        font-size: 14px;
                    }
                }
            }

            .link-btn {
                color: $header-color;
            }
        }
    }

    &:hover,&.active {
        background-color: $theme-color;

        .news-image {
            img {
                &:first-child {
                    -webkit-transform: translateX(0) scaleX(1);
                    transform: translateX(0) scaleX(1);
                    opacity: 1;
                    -webkit-filter: blur(0);
                    filter: blur(0)
                }

                &:nth-child(2){
                    -webkit-transform: translateX(-50%) scaleX(2);
                    transform: translateX(-50%) scaleX(2);
                    opacity: 0;
                    -webkit-filter: blur(10px);
                    filter: blur(10px);
                }
            }
        }

        .news-content {
    
            .post-list {
                li {
                    color: $white;
                }
            }
    
            h4 {
                a {
                    color: $white;
                }
            }
    
            .author-items {
                border-top: 1px solid $white;
                .author-info {
                    .content {
                        h6 {
                           color: $white;
                        }
    
                        p {
                            color: $white;
                        }
                    }
                }
            }

            .link-btn {
                color: $white;
            }
        }
    }
}

.news-card-items {
    margin-top: 30px;
    padding: 30px;
    border: 1px solid rgb(222, 222, 222);
    position: relative;
    @include transition;

    .news-thumb {
        position: relative;
        z-index: 9;

        img {
            @include imgw;
        }

        .news-date {
            position: absolute;
            right: 0;
            bottom: -30px;
            span {
                background: $theme-color;
                color: $white;
                width: 60px;
                height: 30px;
                display: block;
                font-size: 18px;
                font-weight: 700;
                text-align: center;

                &:nth-child(2){
                    background-color: $header-color;
                }
            }
        }
    }

    .news-content {
        margin-top: 20px;
        position: relative;
        z-index: 9;

        .news-tag {
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
            gap: 10px;

            a {
                background: #f5f5f8;
                padding: 0px 12px;
                font-size: 14px;
                color: $text-color;
                font-weight: 500;

                &:hover {
                    background: $theme-color;
                    color: $white;
                }
            }
        }

        h3 {
            font-size: 26px;

            @include breakpoint (max-xxl){
                font-size: 22px;
            }
            
            a {
                &:hover {
                    color: $theme-color;
                }
            }
        }

        .client-info {
            margin-top: 30px;
            @include flex;
            gap: 15px;

            .client-img {
                width: 70px;
                height: 70px;
                border-radius: 50%;
                background-color: $bg-color;
            }
        }
    }

    &::before {
        position: absolute;
        content: "";
        top: 0;
        left: 0;
        width: 0;
        height: 0;
        border-top: 1px solid $theme-color;
        border-left: 1px solid $theme-color;
        transition: 0.5s;
        opacity: 0;
        visibility: hidden;
    }

    &::after {
        position: absolute;
        content: "";
        bottom: 0;
        right: 0;
        width: 0;
        height: 0;
        border-bottom: 1px solid $theme-color;
        border-right: 1px solid $theme-color;
        transition: 0.5s;
        opacity: 0;
        visibility: hidden;
    }

    &:hover {
        transform: translateY(-10px);

        &::after {
            width: 100%;
            height: 100%;
            visibility: visible;
            opacity: 1;
        }

        &::before {
            width: 100%;
            height: 100%;
            visibility: visible;
            opacity: 1;
        }
    }
}

.single-news-style-1 {
    background-color: $white;
    margin-top: 30px;
    box-shadow: $shadow;

    .news-image {
        padding: 10px 10px 0px 10px;
        position: relative;
        
        img {
            @include imgw;
        }

        .post-cat {
            position: absolute;
            bottom: 0;
            right: 8%;

            .cat-name {
                color: $white;
                font-weight: 500;
                padding: 10px 24px;
                background-color: $theme-color;
                display: inline-block;
                @include transition;
                font-size: 16px;

                &:hover {
                    background-color: $header-color;
                }

                @include breakpoint(max-sm){
                    font-size: 14px;
                    padding: 10px 12px;
                }
            }
        }
    }

    .news-content {
        padding: 30px 35px;

        .author-item {
            @include flex;
            gap: 30px;

            @include breakpoint(max-xxl){
                flex-wrap: wrap;
                gap: 15px;
            }

            ul {
                li {
                    font-size: 16px;
                    font-weight: 500;
                    @include breakpoint(max-sm){
                       font-size: 15px;
                    }

                    i {
                        margin-right: 10px;
                    }
                }
            }

            .post-author {
                gap: 12px;

                p {
                    font-weight: 500;
                }
            }
        }

        h3 {
            margin-bottom: 15px;

            a {
                -webkit-transition: all .2s ease;
                -moz-transition: all .2s ease;
                -o-transition: all .2s ease;
                transition: all .2s ease;
                margin: 0;
                text-transform: none;
                background-position: 0 95%;
                background-repeat: no-repeat;
                background-size: 0% 2px;
                display: inline;
                -webkit-transition: all .5s ease;
                -moz-transition: all .5s ease;
                -o-transition: all .5s ease;

                &:hover{
                    background-size: 100% 2px;
                    background-image: linear-gradient($theme-color,$theme-color);
                    color: $theme-color;
                }
            }
        }
        
        @include breakpoint(max-sm){
            padding: 25px 30px;
        }

        .link-btn {
            margin-top: 20px;
            display: inline-block;
        }
    }
} 

.news-box-items-2 {
    margin-top: 30px;
    @include flex;
    gap: 30px;

    @include breakpoint (max-xxl){
        flex-wrap: wrap;
    }

    .news-image {
        max-width: 325px;

        @include breakpoint (max-xxl){
           max-width: 500px
        }

        img {
            @include imgw;
        }
    }

    .news-content {
        .post-cat {
            background: $color-gradient-1;
            text-align: center;
            color: $white;
            padding: 6px 8px;
            line-height: 1;
            text-transform: capitalize;
            font-size: 16px;
            display: inline-block;
            letter-spacing: 2px;
            margin-bottom: 15px;
        }

        h3 {
            margin-bottom: 10px;

            a {
                &:hover {
                    color: $theme-color;
                }
            }
        }

        p {
            margin-bottom: 10px;
        }
    }
}