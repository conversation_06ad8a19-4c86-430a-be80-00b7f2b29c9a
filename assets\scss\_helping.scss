
//page scroll bar add
::-webkit-scrollbar {
	width: 8px;
	height: 8px;
}
/* Track */
::-webkit-scrollbar-track {
	box-shadow: inset 0 0 5px $theme-color; 
	border-radius: 5px;
}
/* Handle */
::-webkit-scrollbar-thumb {
	background: $theme-color;
	border-radius: 10px;
}
//page scroll bar add

//Basic Code Start
.fix {
  overflow: hidden;
}

.ralt{
 position: relative;
}

.ml-100{
  margin-left: 100px;
}
//Basic Code End

//Video Css Start
.video-pulse::after,
.video-pulse::before {
  position: absolute;
  content: "";
  width: 90px;
  height: 90px;
  border: 1px solid $white;
  opacity: 0.7;
  left: 0;
  top: 0;
  border-radius: 50%;
  -webkit-animation-duration: 2.5s;
  animation-duration: 2.5s;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
  -webkit-animation-name: video-animation;
  animation-name: video-animation;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}

.video-pulse::before {
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
.ripple {
  position: relative;

  &::before,&::after {
      position: absolute;
      left: 50%;
      top: 50%;
      width: 60px;
      height: 60px;
      -webkit-transform: translateX(-50%) translateY(-50%);
      transform: translateX(-50%) translateY(-50%);
      border-radius: 50%;
      box-shadow: 0 0 0 0 rgba(214, 17, 30, 0.5);
      -webkit-animation: rippleOne 3s infinite;
      animation: rippleOne 3s infinite;
  }

  &::before {
      -webkit-animation-delay: 0.9s;
      animation-delay: 0.9s;
      content: "";
      position: absolute;
      right: 0;
      bottom: 0;
  }

  &::after {
      -webkit-animation-delay: 0.6s;
      animation-delay: 0.6s;
      content: "";
      position: absolute;
      right: 0;
      bottom: 0;
  }
}
//Video Css End


//pagination default
.swiper-dot {
  text-align: center;

  .swiper-pagination-bullet {
      width: 30px;
      height: 8px;
      transition: 0.6s;
      background-color: $header-color;
      opacity: 1;
      border-radius: 60px;
      position: relative;
  }
  .swiper-pagination-bullet.swiper-pagination-bullet-active {
      background: $color-gradient-1;
      transition: 0.6s;
      position: relative;
      width: 51px;
  }

}

.swiper-dot-2 {
  .swiper-pagination-bullet {
      width: 20px;
      height: 20px;
      transition: 0.6s;
      background-color: $white;
      opacity: 1;
      border-radius: 50px;
      position: relative;
      border: 2px solid $white;
      display: inline-block;
      margin: 3px 0px !important;

  }
  .swiper-pagination-bullet.swiper-pagination-bullet-active {
      background-color: transparent;
      transition: 0.6s;
      position: relative;
  }

}

.array-button {
    @include flex;
    gap: 10px;

    .array-prev, .array-next {
      width: 50px;
      height: 50px;
      line-height: 50px;
      text-align: center;
      border-radius: 50%;
    }

    .array-prev {
        background-color: $theme-color;
        color: $white;
        @include transition;

        &:hover {
            background-color: $header-color;
        }
    }

    .array-next {
      background-color: $header-color;
      color: $white;
      @include transition;

      &:hover {
          background-color: $theme-color;
      }
  }
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: -20px;
}

br {
  @include breakpoint(max-lg){
    display: none;
  }
}


/* background */
.bg-cover {
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  background-position: center;
}

//>>>>> Nice Select Css Start <<<<<//
.nice-select {
    color: $text-color;
    background-color: transparent;
    font-size: 16px;
    width: unset;
    outline: none;
    padding: 18px 30px;
    border: none;
    border: 1px solid #E4E4E4;
    font-weight: 500;
    color: $text-color;

    @include breakpoint (max-sm){
      padding: 12px 20px;
   }
   
   @include transition;

   span {
        color: $text-color;
        font-size: 16px;
        margin-bottom: 0 !important;
        font-weight: 500;
    }

    &:hover {

      &::after {
        border-bottom: 1px solid $text-color;
        border-right: 1px solid $text-color;
      }
  }
}

.nice-select option {
  color: $text-color;
}

.nice-select .current {
  margin-right: 12px;
}

.nice-select:after {
  right: 23px;
  border-bottom: 1px solid $text-color;
  border-right: 1px solid $text-color;
  width: 10px;
  height: 10px;
}

.nice-select.open .list {
  background: $bg-color;
  margin-top: 16px;
  width: 100%;
  text-transform: capitalize;
  color: $text-color;
}

.nice-select .option.selected.focus {
  background: $bg-color;
  outline: none;
  color: $theme-color;
  text-transform: capitalize;
  font-size: 16px;
}

.nice-select .option {
  border: none;
}

.nice-select .option:hover {
background: transparent;
}
//>>>>> Nice Select Css End <<<<<//


.head-color {
  color: $header-color;
}

.theme-color-1 {
  color: $theme-color !important;
}

.border-none {
  border-top: none !important;
}

.mbm-10 {
    margin-bottom: -20px;
}

.margin-minus-10 {
  margin-top: -10px;
}

.ml-30 {
    margin-left: 30px;

    @include breakpoint (max-xl){
      margin-left: 0;
    }
}

.box-shadow {
    box-shadow: $shadow;
}

.mt-30 {
    margin-top: 30px;
}

.page-nav-wrap {
  ul {
      li {
          display: inline-block;

          .page-numbers {
              &.current {
                  background-color: $theme-color;
                  color: $white;
              }

              display: inline-block;
              width: 60px;
              height: 60px;
              line-height: 60px;
              border-radius: 50%;                
              background: #f6f6f6;
              font-weight: 700;
              transition: all 0.3s ease-in-out;
              margin: 0 2px;

              @media (max-width: 767px) {
                  margin-top: 10px;
                  width: 50px;
                  height: 50px;
                  line-height: 50px;
                  font-size: 14px;
              }

              i {
                  margin-top: 2px;
              }
              &:hover {
                  background-color: $theme-color;
                  color: $white;
              }
          }
      }
  }
}
.border-top-none {
  border-top: none !important;
}