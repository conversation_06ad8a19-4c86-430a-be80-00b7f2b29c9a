.contact-wrapper {
    .contact-content {
        .icon-items {
            background-color: $white;
            box-shadow: 0px 26px 81px 0px rgba(0, 0, 0, 0.09);
            padding: 30px 20px;
            text-align: center;

            .icon {
                margin-bottom: 15px;
                padding: 0 15px;
            }
        }
    }

    .contact-items {
        margin-left: 30px;
        background-color: $white;
        box-shadow: $shadow;
        padding: 50px 40px;

        @include breakpoint (max-xl){
            margin-left: 0;
        }

        .form-clt {
            input,textarea {
                width: 100%;
                outline: none;
                border: none;
                background-color: #f3f3f3;
                padding: 16px 30px;
                color: $text-color;
                text-transform: capitalize;
            }

            textarea {
                padding-bottom: 150px;
            }
        }
    }
}

.single-contact-card {
    background-color: #f8f8f8;
    padding: 40px;
    transition: all .3s ease-in-out;

    @media (max-width: 585px) {
        padding: 30px;
    }

    &:hover {
        background-color: $theme-color;

        .icon {
            color:$theme-color !important;
            background-color: $white !important;
        }

        .bottom-part {
            .icon {
                color: $theme-color !important;
                background-color: $white !important;
            }
        }

        p,
        span, h4 {
            color: #fff;
        }
    }

    .top-part {
        overflow: hidden;

        .icon {
            width: 70px;
            height: 70px;
            font-size: 20px;
            line-height: 70px;
            border-radius: 50%;
            background-color: #fff;
            text-align: center;
            float: left;
            color: $theme-color;
            overflow: hidden;
            margin-right: 20px;
            @include transition;
        }

        .title {
            overflow: hidden;
        }
    }

    .bottom-part {
        margin-top: 25px;
        overflow: hidden;

        .icon {
            width: 50px;
            height: 50px;
            font-size: 14px;
            line-height: 50px;
            border-radius: 50%;
            background-color: #fff;
            text-align: center;
            float: right;
            overflow: hidden;
            margin-left: 20px;
            @include transition;
        }

        .info {
            display: inline-block;
            overflow: hidden;
        }
    }
}

.contact-form-items {
    background-color: $bg-color;
    padding: 70px;

    @include breakpoint (max-md){
        padding: 60px 40px;
    }

    @include breakpoint (max-sm){
        padding: 50px 30px;
    }

    .title {
        margin-bottom: 40px;

        h2 {
            font-size: 36px;
            margin-bottom: 5px;
        }
    }

    .form-clt {
        input,textarea {
            padding: 18px 30px;
            border: none;
            outline: none;
            background-color: $white;
            width: 100%;
            font-size: 16px;
            font-weight: 500;
            color: $text-color;

            @include breakpoint (max-sm){
               padding: 12px 20px;
            }
        }

        textarea {
            padding: 18px 30px 110px;

            @include breakpoint (max-sm){
                padding: 12px 20px 90px;
            }
        }

        &::placeholder {
            color: $text-color;
        }
    }
}

.office-google-map-wrapper {

    iframe {
        width: 100%;
        height: 600px;
    }
}