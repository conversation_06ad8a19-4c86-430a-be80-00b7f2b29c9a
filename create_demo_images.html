<!DOCTYPE html>
<html>
<head>
    <title>Demo Interface Generator</title>
    <style>
        .demo-interface {
            width: 400px;
            height: 250px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
        }
        
        .ecommerce-demo {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
        }
        
        .inventory-demo {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            padding: 20px;
        }
        
        .pos-demo {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            padding: 20px;
        }
        
        .mlm-demo {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            padding: 20px;
        }
        
        .demo-header {
            background: rgba(255,255,255,0.2);
            padding: 10px;
            margin-bottom: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .demo-content {
            display: flex;
            gap: 10px;
        }
        
        .demo-sidebar {
            width: 30%;
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 5px;
        }
        
        .demo-main {
            width: 70%;
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 5px;
        }
        
        .demo-item {
            background: rgba(255,255,255,0.2);
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h2>Demo Interface Screenshots</h2>
    <p>Right-click on each demo interface and "Save image as" to save them in assets/img/demos/ folder</p>
    
    <h3>E-commerce Demo Interface</h3>
    <div class="demo-interface ecommerce-demo" id="ecommerce">
        <div class="demo-header">🛒 E-commerce Platform Dashboard</div>
        <div class="demo-content">
            <div class="demo-sidebar">
                <div class="demo-item">📊 Dashboard</div>
                <div class="demo-item">🛍️ Products</div>
                <div class="demo-item">📦 Orders</div>
                <div class="demo-item">👥 Customers</div>
                <div class="demo-item">💰 Sales</div>
            </div>
            <div class="demo-main">
                <div class="demo-item">📈 Sales Chart</div>
                <div class="demo-item">🔥 Top Products</div>
                <div class="demo-item">📋 Recent Orders</div>
                <div class="demo-item">💳 Payment Methods</div>
            </div>
        </div>
    </div>
    
    <h3>Inventory Management Demo Interface</h3>
    <div class="demo-interface inventory-demo" id="inventory">
        <div class="demo-header">📦 Inventory Management System</div>
        <div class="demo-content">
            <div class="demo-sidebar">
                <div class="demo-item">📊 Dashboard</div>
                <div class="demo-item">📦 Stock</div>
                <div class="demo-item">🏪 Warehouses</div>
                <div class="demo-item">🚚 Suppliers</div>
                <div class="demo-item">📋 Reports</div>
            </div>
            <div class="demo-main">
                <div class="demo-item">📊 Stock Levels</div>
                <div class="demo-item">⚠️ Low Stock Alerts</div>
                <div class="demo-item">📈 Inventory Trends</div>
                <div class="demo-item">🔄 Stock Movements</div>
            </div>
        </div>
    </div>
    
    <h3>POS System Demo Interface</h3>
    <div class="demo-interface pos-demo" id="pos">
        <div class="demo-header">💳 Point of Sale System</div>
        <div class="demo-content">
            <div class="demo-sidebar">
                <div class="demo-item">🛒 Cart</div>
                <div class="demo-item">🔍 Search</div>
                <div class="demo-item">💰 Payment</div>
                <div class="demo-item">🧾 Receipt</div>
                <div class="demo-item">📊 Reports</div>
            </div>
            <div class="demo-main">
                <div class="demo-item">🛍️ Product Grid</div>
                <div class="demo-item">💳 Payment Terminal</div>
                <div class="demo-item">📱 Mobile Support</div>
                <div class="demo-item">🎯 Quick Actions</div>
            </div>
        </div>
    </div>
    
    <h3>MLM Software Demo Interface</h3>
    <div class="demo-interface mlm-demo" id="mlm">
        <div class="demo-header">🌐 MLM Binary System</div>
        <div class="demo-content">
            <div class="demo-sidebar">
                <div class="demo-item">👤 Profile</div>
                <div class="demo-item">🌳 Genealogy</div>
                <div class="demo-item">💰 Commissions</div>
                <div class="demo-item">👥 Team</div>
                <div class="demo-item">📊 Reports</div>
            </div>
            <div class="demo-main">
                <div class="demo-item">🌳 Binary Tree</div>
                <div class="demo-item">💵 Earnings Chart</div>
                <div class="demo-item">🎯 Achievements</div>
                <div class="demo-item">📈 Growth Stats</div>
            </div>
        </div>
    </div>

    <script>
        // Convert divs to canvas and download as images
        function downloadAsImage(elementId, filename) {
            const element = document.getElementById(elementId);
            html2canvas(element).then(canvas => {
                const link = document.createElement('a');
                link.download = filename;
                link.href = canvas.toDataURL();
                link.click();
            });
        }
        
        // Add download buttons
        document.addEventListener('DOMContentLoaded', function() {
            const demos = ['ecommerce', 'inventory', 'pos', 'mlm'];
            demos.forEach(demo => {
                const element = document.getElementById(demo);
                const button = document.createElement('button');
                button.textContent = `Download ${demo} image`;
                button.onclick = () => downloadAsImage(demo, `${demo}-demo-interface.jpg`);
                element.parentNode.insertBefore(button, element.nextSibling);
            });
        });
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
</body>
</html>
