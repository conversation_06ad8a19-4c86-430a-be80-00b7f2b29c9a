.service-wrapper {
    margin-right: -25%;

    @include breakpoint (max-md){
        margin-right: 0;
    }

    .service-image {
        margin-top: 30px;
        position: relative;

        img {
            @include imgw;
            border-radius: 15px;
        }

        &::before {
             background-image: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 19.36%, rgba(112, 0, 254, 0.55) 71.26%, #7000fe 100%);
             background-repeat: no-repeat;
             background-size: cover;
             background-position: center;
             position: absolute;
             top: 0;
             left: 0;
             right: 0;
             bottom: 0;
             width: 100%;
             height: 100%;
             content: "";
             overflow: hidden;
             opacity: 0;
             -webkit-transition: all 0.3s ease-in-out;
             transition: all 0.3s ease-in-out;
             visibility: hidden;
             content: "";
             border-radius: 0 0 15px 15px;
         }

        .icon {
            position: absolute;
            top: 0;
            left: 50%;
            width: 90px;
            height: 90px;
            border-radius: 50%;
            line-height: 95px;
            background-color: $white;
            text-align: center;
            color: $theme-color;
            font-size: 20px;
            transform: translate(-50%,-50%) rotate(-40deg);
            @include transition;
            opacity: 0;
            visibility: hidden;

            @include breakpoint (max-sm){
                width: 60px;
                height: 60px;
                line-height: 65px;
            }

            &:hover {
                background-color: $theme-color;
                color: $white;
                transform: translate(-50%,-50%) rotate(0);
                
            }
        }

        .service-content {
            position: absolute;
            bottom: 30px;
            left: 30px;
            background-color: $white;
            padding: 15px 30px;
            border-radius: 20px;
            @include transition;

            h3 {
                a {
                    &:hover {
                        color: $theme-color;
                    }
                }
            }
        }

        &:hover {
            &::before {
                opacity: 1;
                visibility: visible;
            }

            .service-content {
                background-color: transparent;

                h3 {
                    a {
                        color: $white;
                    }
                }
            }
            .icon {
                opacity: 1;
                visibility: visible;
                top: 50%;
            }
        }
    }
}

.service-wrapper-2 {
    .service-box-items {
        background-color: $white;
        box-shadow: $shadow;
        padding: 40px 25px;

        &:not(:last-child){
            margin-bottom: 30px;
        }

        .icon {
            width: 110px;
            height: 110px;
            line-height: 115px;
            text-align: center;
            border-radius: 50%;
            font-size: 52px;
            background: $color-gradient-1;
            text-align: center;
            color: $white;
        }

        .content {
            margin-top: 25px;

            h3 {
                margin-bottom: 10px;

                a {
                    &:hover {
                        color: $theme-color;
                    }
                }
            }
        }

        &.style-2 {
            margin-top: 50px;
        }
    }

    .service-right-content {
        margin-left: 40px;

        @include breakpoint (max-xl){
            margin-left: 0;
        }

        p {
            margin-bottom: 30px;
        }

        .icon-items {
            @include flex;
            gap: 15px;

            &:not(:last-child){
                margin-bottom: 30px;
            }

            .icon {
                width: 70px;
                height: 70px;
                line-height: 70px;
                text-align: center;
                border-radius: 50%;
                background-color: #f4f4f4;
            }
        }

        .theme-btn {
            margin-top: 40px;

            @include breakpoint (max-md){
                margin-top: 30px;
            }

            @include breakpoint (max-sm){
                margin-top: 25px;
            }
        }
    }
}

.service-section {
    position: relative;

    .bg-shape {
        position: absolute;
        top: 0;
        left: -20%;
        z-index: -1;
    }
}

.service-card-items {
    margin-top: 30px;
    @include flex;
    gap: 20px;
    background-color: $white;
    box-shadow: $shadow;
    padding: 50px;

    .service-content {
        h3 {
            margin-bottom: 10px;

            a {
                &:hover {
                    color: $theme-color;
                }
            }
        }

        p {
            margin-bottom: 15px;
        }

        .link-btn {
            &:hover {
                color: $theme-color;
            }
        }
    }

    @include breakpoint (max-md){
        padding: 30px;
        flex-wrap: wrap;
        justify-content: center;
        text-align: center;
    }
}

.service-details-wrapper {
    .service-details-image {
        img {
            @include imgw;
        }
    }

    .service-details-content {
        margin-top: 30px;

        h3 {
            font-size: 36px;
        }

        p {
            font-weight: 400;
        }

        .service-details-video {
            margin-top: 50px;
            margin-bottom: 40px;

            @include breakpoint (max-sm){
                margin-top: 25px;
                margin-bottom: 30px;
             }

            @include breakpoint (max-sm){
                margin-bottom: 20px;
                margin-top: 20px;
            }

            .details-video-content {
                ul {

                    li {
                        font-weight: 500;
                        font-size: 18px;
                        color: $header-color;

                        &:not(:last-child){
                            margin-bottom: 15px;
                        }

                        i {
                            color: $theme-color;
                            margin-right: 8px;
                        }
                    }
                }
            }

            .video-image {
                position: relative;

                img {
                    @include imgw;
                }

                .video-box {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%,-50%);

                    .video-btn {
                        background-color: $white;
                        width: 80px;
                        line-height: 80px;
                        height: 80px;
                        text-align: center;
                        color: $theme-color;
                        border-radius: 50%;
                        display: inline-block;
                    }

                    .ripple {
                        &::before,&::after {
                            width: 80px;
                            height: 80px;
                            box-shadow: 0 0 0 0 rgba(8, 189, 201, 0.6);
                        }
                    }
                }
            }
        }

        .highlight-text {
            margin-top: 30px;
            padding: 18px;
            border-left: 4px solid $theme-color;
            box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.06);
            background: $white;

            h5 {
                font-weight: 600;
                line-height: 167%;
            }
        }

        .service-image-item {
            margin-top: 60px;
            margin-bottom: 60px;

            h3 {
                font-size: 24px;
            }

            .service-box-image {
                height: 218px;

                img {
                    @include imgw;
                    object-fit: cover;
                }
            }
        }
    }
}

.service-sidebar{
    .service-widget-categories {
        margin-bottom: 30px;
        background-color: $bg-color;
        padding: 30px;

        h4 {
            margin-bottom: 30px;
            font-size: 22px;
        }
        
        ul {
            li {
                
                @include flex;
                justify-content: space-between;
                padding: 22px 25px;
                background-color: $white;
                font-weight: 500;
                @include transition;
                border-radius: 0;
                font-size: 18px;
                line-height: 1;
                font-weight: 500;
                font-family: $heading-font;

                a {
                    color: $header-color;
                }

                span {
                    @include transition;
                    color: $header-color;
                }

                &:not(:last-child){
                    margin-bottom: 12px;
                }

                &:hover {
                    background-color: $theme-color;

                    a {
                        color: $white;
                    }

                    span {
                        color: $white;
                    }
                }


                &.active {
                    background-color: $theme-color;

                    a {
                        color: $white;
                    }

                    span {
                        color: $white;
                    }
                }
            }
        }
    }

    .contact-bg {
        padding: 40px 25px;
        text-align: center;

        .icon {
            width: 86.526px;
            height: 76px;
            text-align: center;
            line-height: 76px;
            border-radius: 4px;
            background-color: $theme-color;
            font-size: 32px;
            color: $white;
            margin: 0 auto 25px;
        }

        h3 {
            font-size: 26px;
            color: $white;
            line-height: 139%;
            margin-bottom: 30px;
        }

        p {
            color: $white;
            font-size: 15px;
            font-weight: 400;
            letter-spacing: 1.95px;
            text-transform: uppercase;
            margin-bottom: 15px;
        }

        .theme-btn {
            padding: 18.5px 50px;
            background-color: $theme-color;

            &::before {
                background-color: $header-color;
            }
        }
    }
}