/*
Theme Name: Printnow
Author: Modina Theme
Author URI: https://themeforest.net/user/modinatheme/
Description: Printing Company & Design Services HTML Template
Version: 1.1.0
*/
@import url("https://fonts.googleapis.com/css2?family=Outfit:wght@100..900&display=swap");
@import url("https://fonts.cdnfonts.com/css/satoshi");
:root {
  --body: #fff;
  --black: #000;
  --white: #fff;
  --theme: #7000fe;
  --theme2: #7000fe;
  --header: #09052F;
  --base: #7000fe;
  --text: #767676;
  --text2: #8A8C94;
  --border: #EEEFF4;
  --button: #1C2539;
  --button2: #030734;
  --ratting: #F09815;
  --bg: #f3f5ff;
  --color-gradient-1: linear-gradient(90.07deg, #FF4E8D 32.43%, #AE34E8 58.79%, #7000fe 105.32%);
  ---box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
}

.theme-btn {
  background-color: var(--theme);
  color: var(--white);
  display: inline-block;
  font-size: 16px;
  font-weight: 700;
  padding: 23px 54px;
  border-radius: 0;
  text-transform: uppercase;
  transition: all 0.3s ease-in-out;
  position: relative;
  z-index: 1;
  line-height: 1;
}
.theme-btn::before {
  content: "";
  background-color: var(--header);
  width: 0;
  height: 50%;
  position: absolute;
  top: 0;
  left: 0;
  transition: all 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  z-index: -1;
}
.theme-btn::after {
  content: "";
  background-color: var(--header);
  width: 0;
  height: 50%;
  position: absolute;
  bottom: 0;
  right: 0;
  transition: all 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  z-index: -1;
}
.theme-btn.border-style {
  background-color: transparent;
  color: var(--header);
  border: 1px solid var(--border);
}
.theme-btn.border-style::before, .theme-btn.border-style::after {
  background-color: var(--theme);
}
.theme-btn i {
  margin-left: 10px;
}
.theme-btn:hover {
  color: var(--white);
}
.theme-btn:hover::before, .theme-btn:hover::after {
  width: 100%;
}
.theme-btn.bg-white {
  background-color: var(--white);
  color: var(--header);
}
.theme-btn.bg-white:hover {
  color: var(--white);
}
@media (max-width: 767px) {
  .theme-btn {
    padding: 20px 32px;
  }
}
@media (max-width: 575px) {
  .theme-btn {
    padding: 18px 30px;
    font-size: 14px;
  }
}

.link-btn {
  text-transform: capitalize;
  font-size: 16px;
  color: var(--header);
  font-weight: 600;
}
.link-btn span {
  background-image: linear-gradient(var(--theme), var(--theme));
  background-position: 0 95%;
  background-repeat: no-repeat;
  background-size: 0% 2px;
  display: inline-block;
  transition: all 0.4s ease-in-out;
}
.link-btn i {
  margin-left: 10px;
  font-size: 16px;
  transition: all 0.4s ease-in-out;
  font-size: 12px;
  width: 25px;
  height: 25px;
  line-height: 25px;
  border-radius: 25px;
  background-color: var(--theme);
  text-align: center;
  color: var(--white);
}
.link-btn:hover span {
  color: var(--theme);
  background-size: 100% 1px;
}
.link-btn.link-btn-2 span {
  background-image: linear-gradient(var(--theme2), var(--theme2));
}
.link-btn.link-btn-2 i {
  background-color: var(--theme2);
}
.link-btn.link-btn-2:hover span {
  color: var(--theme2);
}

/* --------------------------------------------
    Template Default Fonts & Fonts Styles
 ---------------------------------------------- */
body {
  font-family: "Satoshi", sans-serif;
  font-size: 16px;
  font-weight: normal;
  line-height: 28px;
  color: var(--text);
  background-color: var(--white);
  padding: 0;
  margin: 0;
  overflow-x: hidden;
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

button {
  border: none;
  background-color: transparent;
  padding: 0;
}

input:focus {
  color: var(--white);
  outline: none;
}

input {
  color: var(--white);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Outfit", sans-serif;
  margin: 0px;
  padding: 0;
  color: var(--header);
  text-transform: initial;
  transition: all 0.4s ease-in-out;
}

h1 {
  font-size: 70px;
  font-weight: 700;
  line-height: 115%;
}

h2 {
  font-size: 50px;
  line-height: 110%;
  font-weight: 700;
}
@media (max-width: 1199px) {
  h2 {
    font-size: 42px;
  }
}
@media (max-width: 767px) {
  h2 {
    font-size: 44px;
  }
}
@media (max-width: 575px) {
  h2 {
    font-size: 38px;
  }
}

h3 {
  font-size: 22px;
  font-weight: 700;
}
@media (max-width: 575px) {
  h3 {
    font-size: 20px;
  }
}

h4 {
  font-size: 20px;
  font-weight: 700;
  line-height: 130%;
}

h5 {
  font-size: 18px;
  font-weight: 700;
}

h6 {
  font-size: 16px;
  font-weight: 700;
}

a {
  text-decoration: none;
  outline: none !important;
  cursor: pointer;
  color: var(--header);
  transition: all 0.4s ease-in-out;
}

p {
  margin: 0px;
  transition: all 0.4s ease-in-out;
}

.preloader {
  align-items: center;
  cursor: default;
  display: flex;
  height: 100%;
  justify-content: center;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 9999999;
}
.preloader .animation-preloader {
  z-index: 1000;
}
.preloader .animation-preloader .spinner {
  animation: spinner 1s infinite linear;
  border-radius: 50%;
  border: 3px solid rgba(0, 0, 0, 0.2);
  border-top-color: var(--theme);
  height: 9em;
  margin: 0 auto 3.5em auto;
  width: 9em;
}
@media (max-width: 767px) {
  .preloader .animation-preloader .spinner {
    width: 7.5em;
    height: 7.5em;
    margin: 0 auto 1.5em auto;
  }
}
.preloader .animation-preloader .txt-loading {
  font: bold 5em "Outfit", sans-serif, "Satoshi", sans-serif;
  text-align: center;
  user-select: none;
}
@media (max-width: 767px) {
  .preloader .animation-preloader .txt-loading {
    font-size: 2.5em;
  }
}
.preloader .animation-preloader .txt-loading .letters-loading {
  color: var(--theme);
  position: relative;
}
.preloader .animation-preloader .txt-loading .letters-loading:nth-child(2):before {
  animation-delay: 0.2s;
}
.preloader .animation-preloader .txt-loading .letters-loading:nth-child(3):before {
  animation-delay: 0.4s;
}
.preloader .animation-preloader .txt-loading .letters-loading:nth-child(4):before {
  animation-delay: 0.6s;
}
.preloader .animation-preloader .txt-loading .letters-loading:nth-child(5):before {
  animation-delay: 0.8s;
}
.preloader .animation-preloader .txt-loading .letters-loading:nth-child(6):before {
  animation-delay: 1s;
}
.preloader .animation-preloader .txt-loading .letters-loading:nth-child(7):before {
  animation-delay: 1.2s;
}
.preloader .animation-preloader .txt-loading .letters-loading:nth-child(8):before {
  animation-delay: 1.4s;
}
.preloader .animation-preloader .txt-loading .letters-loading::before {
  animation: letters-loading 4s infinite;
  color: var(--header);
  content: attr(data-text-preloader);
  left: 0;
  opacity: 0;
  font-family: "Outfit", sans-serif;
  position: absolute;
  top: -3px;
  transform: rotateY(-90deg);
}
.preloader p {
  font-size: 15px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 8px;
  color: var(--theme);
}
.preloader .loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  font-size: 0;
  z-index: 1;
  pointer-events: none;
}
.preloader .loader .row {
  height: 100%;
}
.preloader .loader .loader-section {
  padding: 0px;
}
.preloader .loader .loader-section .bg {
  background-color: #fff;
  height: 100%;
  left: 0;
  width: 100%;
  transition: all 800ms cubic-bezier(0.77, 0, 0.175, 1);
}
.preloader.loaded .animation-preloader {
  opacity: 0;
  transition: 0.3s ease-out;
}
.preloader.loaded .loader-section .bg {
  width: 0;
  transition: 0.7s 0.3s allcubic-bezier(0.1, 0.1, 0.1, 1);
}

.search-wrap {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999999;
  background-color: rgba(255, 255, 255, 0.9);
}
.search-wrap .search-inner {
  position: relative;
  width: 100%;
  height: 100%;
}
.search-wrap .search-cell {
  position: absolute;
  top: 50%;
  width: 100%;
  transform: translateY(-50%);
}
.search-wrap .search-field-holder {
  width: 50%;
  margin: auto;
  position: relative;
  animation: slideInUp 0.3s;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .search-wrap .search-field-holder {
    width: 70%;
  }
}
@media (max-width: 575px) {
  .search-wrap .search-field-holder {
    width: 80%;
  }
}
.search-wrap .main-search-input {
  width: 100%;
  height: 70px;
  border: 0;
  padding: 0 50px;
  text-transform: capitalize;
  background: transparent;
  font-size: 25px;
  color: var(--header);
  border-bottom: 2px solid var(--header);
  text-align: center;
  letter-spacing: 2px;
}

@media (max-width: 575px) {
  .search-wrap .main-search-input {
    height: 50px;
    padding: 0 0;
    line-height: 50px;
    font-size: 18px;
  }
}
.search-wrap input.form-control,
.search-wrap input.form-control:focus {
  background-color: var(--header);
}

input.main-search-input::placeholder {
  color: var(--header);
  opacity: 1;
  font-size: 25px;
}

@media (max-width: 575px) {
  input.main-search-input::placeholder {
    font-size: 18px;
  }
}
.search-close {
  position: absolute;
  top: 50px;
  right: 50px;
  font-size: 30px;
  color: var(--theme);
  cursor: pointer;
}

.mouse-cursor {
  position: fixed;
  left: 0;
  top: 0;
  pointer-events: none;
  border-radius: 50%;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  visibility: hidden;
}
@media (max-width: 767px) {
  .mouse-cursor {
    display: none;
  }
}

.cursor-inner {
  width: 6px;
  height: 6px;
  z-index: 10000001;
  background-color: var(--theme);
  -webkit-transition: width 0.3s ease-in-out, height 0.3s ease-in-out, margin 0.3s ease-in-out, opacity 0.3s ease-in-out;
  -o-transition: width 0.3s ease-in-out, height 0.3s ease-in-out, margin 0.3s ease-in-out, opacity 0.3s ease-in-out;
  transition: width 0.3s ease-in-out, height 0.3s ease-in-out, margin 0.3s ease-in-out, opacity 0.3s ease-in-out;
}

.cursor-inner.cursor-hover {
  margin-left: -35px;
  margin-top: -35px;
  width: 70px;
  height: 70px;
  background-color: var(--theme);
  opacity: 0.3;
}

.cursor-outer {
  margin-left: -12px;
  margin-top: -12px;
  width: 30px;
  height: 30px;
  border: 1px solid var(--theme);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  z-index: 10000000;
  opacity: 0.5;
  -webkit-transition: all 0.08s ease-out;
  -o-transition: all 0.08s ease-out;
  transition: all 0.08s ease-out;
}

.cursor-outer.cursor-hover {
  opacity: 0;
}

.scroll-up {
  cursor: pointer;
  display: block;
  border-radius: 50px;
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  transform: translateY(15px);
  position: fixed;
  right: 25px;
  bottom: 35px;
  height: 50px;
  width: 50px;
  transition: all 0.4s ease-in-out;
}

.scroll-up::after {
  position: absolute;
  font-family: "Font Awesome 5 Pro";
  content: "\f176";
  text-align: center;
  line-height: 50px;
  font-weight: 700;
  font-size: 18px;
  color: var(--theme);
  left: 0;
  top: 0;
  height: 50px;
  width: 50px;
  cursor: pointer;
  display: block;
  z-index: 1;
  transition: all 0.4s ease-in-out;
}

.scroll-up svg path {
  fill: none;
}

.scroll-up svg.scroll-circle path {
  stroke: var(--theme);
  stroke-width: 4px;
  box-sizing: border-box;
  transition: all 0.4s ease-in-out;
}

.scroll-up.active-scroll {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.section-title {
  position: relative;
  z-index: 99;
  margin-bottom: 30px;
}
@media (max-width: 767px) {
  .section-title {
    margin-bottom: 0;
  }
}
.section-title .sub-title {
  padding: 8px 30px;
  border-radius: 30px;
  display: inline-block;
  background: linear-gradient(90deg, rgba(73, 81, 254, 0.112) 24%, rgba(73, 81, 254, 0.112) 38%, rgba(148, 69, 211, 0.112) 62%, rgba(223, 57, 167, 0.112) 100%);
  margin-bottom: 20px;
}
.section-title .sub-title span {
  background: var(--color-gradient-1);
  display: inline-block;
  font-weight: 600;
  text-transform: uppercase;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 14px;
}

.section-title-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 991px) {
  .section-title-area {
    flex-wrap: wrap;
    gap: 0;
    text-align: center;
    justify-content: center;
  }
}
@media (max-width: 767px) {
  .section-title-area {
    gap: 20px;
  }
}

.center {
  text-align: center;
  margin: 0 auto;
}

.section-bg {
  background-color: var(--bg);
}

.section-padding {
  padding: 120px 0;
}
@media (max-width: 1199px) {
  .section-padding {
    padding: 100px 0;
  }
}
@media (max-width: 991px) {
  .section-padding {
    padding: 80px 0;
  }
}

@-webkit-keyframes rippleOne {
  70% {
    -webkit-box-shadow: 0 0 0 40px rgba(244, 68, 56, 0);
    box-shadow: 0 0 0 40px rgba(244, 68, 56, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(244, 68, 56, 0);
    box-shadow: 0 0 0 0 rgba(244, 68, 56, 0);
  }
}
@keyframes rippleOne {
  70% {
    -webkit-box-shadow: 0 0 0 40px rgba(244, 68, 56, 0);
    box-shadow: 0 0 0 40px rgba(244, 68, 56, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(244, 68, 56, 0);
    box-shadow: 0 0 0 0 rgba(244, 68, 56, 0);
  }
}
@keyframes cir36 {
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rounded {
  50% {
    transform: rotate(15deg);
  }
}
@keyframes up-down {
  0% {
    transform: translateY(10px);
  }
  100% {
    transform: translateY(-10px);
  }
}
@-webkit-keyframes spinner {
  to {
    -webkit-transform: rotateZ(360deg);
    transform: rotateZ(360deg);
  }
}
@keyframes spinner {
  to {
    -webkit-transform: rotateZ(360deg);
    transform: rotateZ(360deg);
  }
}
@-webkit-keyframes letters-loading {
  0%, 75%, 100% {
    opacity: 0;
    transform: rotateY(-90deg);
  }
  25%, 50% {
    opacity: 1;
    transform: rotateY(0deg);
  }
}
@keyframes letters-loading {
  0%, 75%, 100% {
    opacity: 0;
    transform: rotateY(-90deg);
  }
  25%, 50% {
    opacity: 1;
    transform: rotateY(0deg);
  }
}
@keyframes loaderspin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
@keyframes tpswing {
  0% {
    -webkit-transform: rotate(20deg);
    -ms-transform: rotate(20deg);
    transform: rotate(20deg);
  }
  100% {
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }
}
@keyframes width {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes width {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes loaderspin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
@keyframes loaderpulse {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.2);
  }
}
@keyframes rounded {
  50% {
    transform: rotate(20deg);
  }
}
@keyframes cir36 {
  100% {
    transform: rotate(360deg);
  }
}
.float-bob-y {
  -webkit-animation-name: float-bob-y;
  animation-name: float-bob-y;
  -webkit-animation-duration: 3s;
  animation-duration: 3s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}

@-webkit-keyframes float-bob-y {
  0% {
    -webkit-transform: translateY(-30px);
    transform: translateY(-30px);
  }
  50% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  100% {
    -webkit-transform: translateY(-30px);
    transform: translateY(-30px);
  }
}
@keyframes float-bob-y {
  0% {
    -webkit-transform: translateY(-30px);
    transform: translateY(-30px);
  }
  50% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  100% {
    -webkit-transform: translateY(-30px);
    transform: translateY(-30px);
  }
}
.float-bob-x {
  -webkit-animation-name: float-bob-x;
  animation-name: float-bob-x;
  -webkit-animation-duration: 3s;
  animation-duration: 3s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}

@-webkit-keyframes float-bob-x {
  0% {
    -webkit-transform: translateX(0px);
    transform: translateX(30px);
  }
  50% {
    -webkit-transform: translateX(10px);
    transform: translateX(10px);
  }
  100% {
    -webkit-transform: translateX(30px);
    transform: translateX(30px);
  }
}
@keyframes float-bob-x {
  0% {
    -webkit-transform: translateX(30px);
    transform: translateX(30px);
  }
  50% {
    -webkit-transform: translateX(10px);
    transform: translateX(10px);
  }
  100% {
    -webkit-transform: translateX(30px);
    transform: translateX(30px);
  }
}
@keyframes bounce-x {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  50% {
    -webkit-transform: translateX(30px);
    transform: translateX(30px);
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
.bounce-x {
  -webkit-animation: bounce-x 7s infinite linear;
  animation: bounce-x 7s infinite linear;
}

@keyframes criss-cross-left {
  0% {
    left: -20px;
  }
  50% {
    left: 50%;
    width: 20px;
    height: 20px;
  }
  100% {
    left: 50%;
    width: 375px;
    height: 375px;
  }
}
@keyframes criss-cross-right {
  0% {
    right: -20px;
  }
  50% {
    right: 50%;
    width: 20px;
    height: 20px;
  }
  100% {
    right: 50%;
    width: 375px;
    height: 375px;
  }
}
@keyframes rotated2 {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(-360deg);
  }
}
@keyframes wave {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(-25%);
  }
  100% {
    transform: translateX(-50%);
  }
}
@keyframes zoom {
  0% {
    transform: scale(0.5);
  }
  50% {
    transform: scale(1);
  }
  100% {
    transform: scale(0.5);
  }
}
@keyframes translateY2 {
  0% {
    -webkit-transform: translateY(-30px);
    -moz-transform: translateY(-30px);
    -ms-transform: translateY(-30px);
    -o-transform: translateY(-30px);
    transform: translateY(-30px);
  }
  100% {
    -webkit-transform: translateY(20px);
    -moz-transform: translateY(20px);
    -ms-transform: translateY(20px);
    -o-transform: translateY(20px);
    transform: translateY(20px);
  }
}
@keyframes translateX2 {
  0% {
    -webkit-transform: translateX(-30px);
    -moz-transform: translateX(-30px);
    -ms-transform: translateX(-30px);
    -o-transform: translateX(-30px);
    transform: translateX(-30px);
  }
  100% {
    -webkit-transform: translatXY(20px);
    -moz-transform: translateX(20px);
    -ms-transform: translateX(20px);
    -o-transform: translateX(20px);
    transform: translateX(20px);
  }
}
@keyframes moving {
  0% {
    transform: translatey(0px);
  }
  20% {
    transform: translateX(-50px);
  }
  50% {
    transform: translatey(-40px);
  }
  100% {
    transform: translatey(0px);
  }
}
@keyframes translateX2 {
  0% {
    -webkit-transform: translateX(-30px);
    -moz-transform: translateX(-30px);
    -ms-transform: translateX(-30px);
    -o-transform: translateX(-30px);
    transform: translateX(-30px);
  }
  100% {
    -webkit-transform: translatXY(20px);
    -moz-transform: translateX(20px);
    -ms-transform: translateX(20px);
    -o-transform: translateX(20px);
    transform: translateX(20px);
  }
}
@keyframes translateY2 {
  0% {
    -webkit-transform: translateY(-30px);
    -moz-transform: translateY(-30px);
    -ms-transform: translateY(-30px);
    -o-transform: translateY(-30px);
    transform: translateY(-30px);
  }
  100% {
    -webkit-transform: translateY(20px);
    -moz-transform: translateY(20px);
    -ms-transform: translateY(20px);
    -o-transform: translateY(20px);
    transform: translateY(20px);
  }
}
.img-custom-anim-top {
  animation: img-anim-top 1.3s forwards cubic-bezier(0.645, 0.045, 0.355, 1);
  opacity: 0;
}

@keyframes img-anim-top {
  0% {
    transform: translateY(-5%);
    clip-path: inset(0 0 100% 0);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    clip-path: inset(0 0 0 0);
    opacity: 1;
  }
}
.header-top-section {
  background: linear-gradient(90.07deg, #FF4E8D 32.43%, #AE34E8 58.79%, #3E8DFF 105.32%);
  position: relative;
  z-index: 9;
}
@media (max-width: 991px) {
  .header-top-section {
    display: none;
  }
}
.header-top-section .container-fluid {
  padding: 0 250px;
}
@media (max-width: 1600px) {
  .header-top-section .container-fluid {
    padding: 0 40px;
  }
}
@media (max-width: 1199px) {
  .header-top-section .container-fluid {
    padding: 0 30px;
  }
}

.header-top-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 99;
}
.header-top-wrapper .contact-list {
  display: flex;
  align-items: center;
  gap: 40px;
}
.header-top-wrapper .contact-list li {
  color: var(--white);
  font-weight: 400;
}
.header-top-wrapper .contact-list li a {
  color: var(--white);
}
.header-top-wrapper .contact-list li i {
  color: var(--white);
  margin-right: 10px;
}
.header-top-wrapper p {
  font-size: 18px;
  font-weight: 500;
  color: var(--white);
  padding: 10px 0;
}
.header-top-wrapper .header-top-right {
  display: flex;
  align-items: center;
  gap: 20px;
}
.header-top-wrapper .header-top-right .social-icon {
  gap: 20px;
}
.header-top-wrapper .header-top-right .social-icon a {
  color: var(--white);
}
.header-top-wrapper .header-top-right .nice-items {
  margin-right: 15px;
}
.header-top-wrapper .header-top-right .nice-items .nice-select {
  padding: 10px 7px 10px 20px;
  background: transparent;
  border: none;
  text-align: center;
  margin: 0 auto;
  position: relative;
  z-index: 999;
}
.header-top-wrapper .header-top-right .nice-items .nice-select span {
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  color: var(--white);
}
.header-top-wrapper .header-top-right .nice-items .nice-select::after {
  right: -10px;
  top: 24px;
  border-bottom: 1px solid var(--white);
  border-right: 1px solid var(--white);
}
.header-top-wrapper .header-top-right .nice-items .nice-select .list {
  background-color: var(--bg);
  border-radius: 0;
  right: initial;
  font-size: 14px;
  margin-top: 0;
}
.header-top-wrapper .header-top-right .nice-items .nice-select .list li {
  font-weight: 500;
}
.header-top-wrapper .header-top-right .nice-items .nice-select .option {
  border: none;
}

.header-section-2 {
  position: relative;
  z-index: 9999;
  background-color: var(--white);
}
.header-section-2 .container-fluid {
  padding: 0 150px;
}
@media (max-width: 1899px) {
  .header-section-2 .container-fluid {
    padding: 0 50px;
  }
}
@media (max-width: 1600px) {
  .header-section-2 .container-fluid {
    padding: 0 40px;
  }
}
@media (max-width: 1199px) {
  .header-section-2 .container-fluid {
    padding: 0 20px;
  }
}

.header-section-1 {
  background-color: var(--white);
  position: relative;
  z-index: 9999;
}

.header-section {
  position: relative;
  z-index: 9999;
  background-color: var(--white);
}

.header-1 .container-fluid {
  padding: 0 250px;
}
@media (max-width: 1600px) {
  .header-1 .container-fluid {
    padding: 0 40px;
  }
}
@media (max-width: 1199px) {
  .header-1 .container-fluid {
    padding: 0 30px;
  }
}
.header-1 .header-right {
  gap: 30px;
}
@media (max-width: 575px) {
  .header-1 .header-right {
    gap: 18px;
  }
}
.header-1 .header-right .search-icon {
  color: var(--header);
  font-size: 18px;
}
.header-1 .header-right .user-icon {
  color: var(--header);
  font-size: 18px;
}
.header-1 .header-right .menu-cart {
  position: relative;
}
.header-1 .header-right .menu-cart .cart-icon {
  position: relative;
  display: inline-block;
  border-radius: 50%;
}
.header-1 .header-right .menu-cart .cart-icon::before {
  position: absolute;
  top: -7px;
  right: -12px;
  content: "3";
  width: 18px;
  line-height: 18px;
  height: 18px;
  border-radius: 18px;
  background-color: var(--theme);
  color: var(--white);
  font-size: 12px;
  text-align: center;
  font-weight: 500;
}
.header-1 .header-right .menu-cart .cart-icon i {
  color: var(--header);
  font-size: 18px;
}
.header-1 .header-right .menu-cart:hover .cart-box {
  transform: rotateX(0deg);
  visibility: visible;
}
.header-1.style-3 .header-main .main-menu ul li a {
  color: var(--white);
}
.header-1.style-3 .header-main .main-menu ul li .submenu {
  color: var(--header);
}
.header-1.style-3 .header-main .main-menu ul li .submenu li a {
  color: var(--header);
}
.header-1.style-3 .header-main .main-menu ul li .submenu li:hover > a {
  background: var(--theme);
  color: var(--white) !important;
}
.header-1.style-3 .header-main .main-menu ul li .submenu li:hover > a::after {
  color: var(--theme);
}
.header-1.style-3 .header-main .main-menu ul li .submenu li:hover > .submenu {
  -webkit-transform: translateY(1);
  -moz-transform: translateY(1);
  -ms-transform: translateY(1);
  -o-transform: translateY(1);
  transform: translateY(1);
  visibility: visible;
  opacity: 1;
}
.header-1.style-3 .header-main .main-menu ul li .submenu li.has-dropdown > a::after {
  position: absolute;
  top: 50%;
  inset-inline-end: 25px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  color: var(--theme);
}
.header-1.style-3 .header-main .main-menu ul li:hover > a {
  color: var(--theme);
}
.header-1.style-3 .header-main .main-menu ul li:hover > a::after {
  color: var(--theme);
}
.header-1.style-3 .header-main .main-menu ul li:hover > .submenu {
  visibility: visible;
  opacity: 1;
  transform: translateY(0px);
}
.header-1.style-3 .header-logo {
  display: none;
}
.header-1.style-3 .header-right .search-icon {
  color: var(--white);
}
.header-1.style-3 .header-right .user-icon {
  color: var(--white);
}
.header-1.style-3 .header-right .menu-cart .cart-icon i {
  color: var(--white);
}
.header-1.style-3 .sidebar__toggle {
  color: var(--white);
}

.middle-section {
  position: relative;
  z-index: 9;
  border-bottom: 1px solid #d9dadc;
  background-color: var(--white);
}
.middle-section .container-fluid {
  padding: 0 250px;
}
@media (max-width: 1600px) {
  .middle-section .container-fluid {
    padding: 0 40px;
  }
}
@media (max-width: 1199px) {
  .middle-section .container-fluid {
    padding: 0 30px;
  }
}
@media (max-width: 1199px) {
  .middle-section {
    display: none;
  }
}

.main-header-wrapper {
  display: flex;
  align-items: center;
  gap: 100px;
}
@media (max-width: 1899px) {
  .main-header-wrapper {
    gap: 70px;
  }
}
@media (max-width: 1600px) {
  .main-header-wrapper {
    gap: 40px;
  }
}
@media (max-width: 1199px) {
  .main-header-wrapper {
    padding: 15px 0;
  }
}
.main-header-wrapper .main-header-items {
  width: 100%;
}
.main-header-wrapper .main-header-items .header-contact-info-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 50px;
  border: 1px solid #D4DCED;
}
@media (max-width: 1600px) {
  .main-header-wrapper .main-header-items .header-contact-info-area {
    padding: 0 30px;
  }
}
@media (max-width: 1199px) {
  .main-header-wrapper .main-header-items .header-contact-info-area {
    display: none;
  }
}
@media (max-width: 575px) {
  .main-header-wrapper .main-header-items .header-contact-info-area {
    gap: 30px;
  }
}
.main-header-wrapper .main-header-items .header-contact-info-area .contact-info-items {
  display: flex;
  align-items: center;
  gap: 20px;
  justify-content: space-between;
  position: relative;
  padding: 30px 0;
  border-right: 1px solid #D4DCED;
  padding-right: 50px;
}
@media (max-width: 1399px) {
  .main-header-wrapper .main-header-items .header-contact-info-area .contact-info-items {
    border-right: none;
    padding-right: 0;
  }
}
.main-header-wrapper .main-header-items .header-contact-info-area .contact-info-items .icon {
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  position: relative;
  background-color: var(--white);
  border-radius: 50%;
  color: var(--theme);
  box-shadow: 0px 4px 25px rgba(0, 0, 0, 0.06);
  position: relative;
}
@media (max-width: 1399px) {
  .main-header-wrapper .main-header-items .header-contact-info-area .contact-info-items .icon {
    width: 30px;
    height: 30px;
    line-height: 30px;
  }
}
.main-header-wrapper .main-header-items .header-contact-info-area .contact-info-items .icon::before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 64px;
  height: 64px;
  content: "";
  background-color: transparent;
  border-radius: 50%;
  border: 1px solid rgba(30, 32, 35, 0.1);
  transform: translate(-50%, -50%);
}
@media (max-width: 1399px) {
  .main-header-wrapper .main-header-items .header-contact-info-area .contact-info-items .icon::before {
    width: 54px;
    height: 54px;
  }
}
.main-header-wrapper .main-header-items .header-contact-info-area .contact-info-items .content p {
  color: var(--text);
  margin-bottom: 5px;
  text-transform: capitalize;
  font-size: 14px;
  font-weight: 500;
  opacity: 0.7;
}
.main-header-wrapper .main-header-items .header-contact-info-area .contact-info-items .content h3 {
  font-size: 20px;
  color: var(--header);
  text-transform: initial;
}
@media (max-width: 1600px) {
  .main-header-wrapper .main-header-items .header-contact-info-area .contact-info-items .content h3 {
    font-size: 18px;
  }
}
.main-header-wrapper .main-header-items .header-contact-info-area .contact-info-items .content h3 a {
  color: var(--header);
}
.main-header-wrapper .main-header-items .header-contact-info-area .contact-info-items.style-2 {
  border: none;
  padding-right: 0;
}
@media (max-width: 1600px) {
  .main-header-wrapper .main-header-items .header-contact-info-area .header-button .theme-btn {
    font-size: 14px;
    padding: 24px 27px;
  }
}

.header-2 .logo {
  display: none;
}
@media (max-width: 1399px) {
  .header-2 .header-main .main-menu ul li {
    margin-inline-end: 40px;
  }
}
@media (max-width: 1899px) {
  .header-2 .header-main .main-menu ul li .has-homemenu {
    left: -50px;
  }
}
.header-2 .header-right {
  gap: 30px;
}
@media (max-width: 575px) {
  .header-2 .header-right {
    gap: 18px;
  }
}
.header-2 .header-right .search-icon {
  color: var(--header);
  font-size: 18px;
}
.header-2 .header-right .user-icon {
  color: var(--header);
  font-size: 18px;
}
.header-2 .header-right .menu-cart {
  position: relative;
}
.header-2 .header-right .menu-cart .cart-icon {
  position: relative;
  display: inline-block;
  border-radius: 50%;
}
.header-2 .header-right .menu-cart .cart-icon::before {
  position: absolute;
  top: -7px;
  right: -12px;
  content: "3";
  width: 18px;
  line-height: 18px;
  height: 18px;
  border-radius: 18px;
  background-color: var(--theme);
  color: var(--white);
  font-size: 12px;
  text-align: center;
  font-weight: 500;
}
.header-2 .header-right .menu-cart .cart-icon i {
  color: var(--header);
  font-size: 18px;
}
.header-2 .header-right .menu-cart:hover .cart-box {
  transform: rotateX(0deg);
  visibility: visible;
}

.header-section-3 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999;
}

@media (max-width: 991px) {
  .menu-thumb {
    display: none !important;
  }
}

.header-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 1199px) {
  .header-main {
    padding: 15px 0px;
  }
}
.header-main .main-menu ul {
  margin-bottom: 0;
}
.header-main .main-menu ul li {
  position: relative;
  list-style: none;
  display: inline-block;
  margin-inline-end: 50px;
}
@media (max-width: 1399px) {
  .header-main .main-menu ul li {
    margin-inline-end: 40px;
  }
}
.header-main .main-menu ul li:last-child {
  margin-inline-end: 0;
}
.header-main .main-menu ul li a {
  display: inline-block;
  font-size: 16px;
  font-weight: 600;
  color: var(--header);
  padding: 35px 0;
  text-align: left;
  position: relative;
  text-transform: capitalize;
  transition: all 0.4s ease-in-out;
}
.header-main .main-menu ul li a i {
  font-size: 15px;
  font-weight: 600;
  margin-left: 5px;
}
.header-main .main-menu ul li a:hover {
  color: var(--theme) !important;
}
.header-main .main-menu ul li .submenu {
  position: absolute;
  top: 100%;
  inset-inline-start: 0;
  min-width: 240px;
  background: var(--white);
  z-index: 99999;
  visibility: hidden;
  opacity: 0;
  transform-origin: top center;
  color: var(--header);
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  transform: translateY(10px);
  transition: all 0.4s ease-in-out;
}
.header-main .main-menu ul li .submenu li {
  display: block;
  width: 100%;
  margin: 0;
  padding: 0;
}
.header-main .main-menu ul li .submenu li a {
  position: relative;
  z-index: 11;
  font-size: 16px;
  font-weight: 600;
  color: var(--header);
  padding: 0 25px;
  padding-bottom: 11px;
  padding-top: 11px;
  width: 100%;
  border-bottom: 1px solid #eeeeee;
}
.header-main .main-menu ul li .submenu li:last-child a {
  border: none;
}
.header-main .main-menu ul li .submenu li .submenu {
  inset-inline-start: 100%;
  top: 0;
  visibility: hidden;
  opacity: 0;
}
.header-main .main-menu ul li .submenu li:hover > a {
  background: var(--theme);
  color: var(--white) !important;
}
.header-main .main-menu ul li .submenu li:hover > a::after {
  color: var(--theme);
}
.header-main .main-menu ul li .submenu li:hover > .submenu {
  -webkit-transform: translateY(1);
  -moz-transform: translateY(1);
  -ms-transform: translateY(1);
  -o-transform: translateY(1);
  transform: translateY(1);
  visibility: visible;
  opacity: 1;
}
.header-main .main-menu ul li .submenu li.has-dropdown > a::after {
  position: absolute;
  top: 50%;
  inset-inline-end: 25px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  color: var(--theme);
}
.header-main .main-menu ul li .has-home-menu {
  width: 1000px;
  padding: 30px 30px 10px 30px;
  opacity: 0;
  left: -250px;
  visibility: hidden;
  padding: 30px 30px 10px 30px;
}
.header-main .main-menu ul li .has-home-menu .home-menu {
  position: relative;
}
.header-main .main-menu ul li .has-home-menu .home-menu .home-menu-thumb {
  position: relative;
}
.header-main .main-menu ul li .has-home-menu .home-menu .home-menu-thumb .demo-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 70%;
  gap: 10px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease-in-out;
  margin-top: 20px;
}
.header-main .main-menu ul li .has-home-menu .home-menu .home-menu-thumb .demo-button .theme-btn {
  padding: 14px 20px;
  color: var(--white) !important;
  width: initial;
  font-size: 14px;
  text-align: center;
  border-radius: 0px !important;
}
.header-main .main-menu ul li .has-home-menu .home-menu .home-menu-thumb .demo-button .theme-btn:hover {
  color: var(--white) !important;
}
.header-main .main-menu ul li .has-home-menu .home-menu .home-menu-thumb .demo-button .theme-btn:hover::before, .header-main .main-menu ul li .has-home-menu .home-menu .home-menu-thumb .demo-button .theme-btn:hover::after {
  border-radius: 0px;
}
.header-main .main-menu ul li .has-home-menu .home-menu .home-menu-thumb::before {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, var(--theme) 100%);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  overflow: hidden;
  opacity: 0;
  transition: all 0.3s ease-in-out;
  content: "";
}
.header-main .main-menu ul li .has-home-menu .home-menu .home-menu-thumb:hover::before {
  visibility: visible;
  opacity: 1;
}
.header-main .main-menu ul li .has-home-menu .home-menu .home-menu-thumb:hover .demo-button {
  opacity: 1;
  visibility: visible;
  margin-top: 0;
}
.header-main .main-menu ul li .has-home-menu .home-menu .home-menu-thumb:hover.home-menu-btn {
  opacity: 1;
  visibility: visible;
  bottom: 50%;
  transform: translateY(50%);
}
.header-main .main-menu ul li .has-home-menu .home-menu .home-menu-thumb img {
  width: 100%;
}
.header-main .main-menu ul li .has-home-menu .home-menu .home-menu-title {
  text-align: center;
  margin: 15px auto;
  display: inline-block;
  font-size: 16px;
}
.header-main .main-menu ul li:hover > a {
  color: var(--theme);
}
.header-main .main-menu ul li:hover > a::after {
  color: var(--theme);
}
.header-main .main-menu ul li:hover > .submenu {
  visibility: visible;
  opacity: 1;
  transform: translateY(0px);
}

.sidebar__toggle {
  cursor: pointer;
}

.header-middle-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 0;
}
.header-middle-wrapper .search-toggle-box {
  width: 300px;
}
.header-middle-wrapper .search-toggle-box .input-area {
  position: relative;
}
.header-middle-wrapper .search-toggle-box .input-area input {
  outline: none;
  border: 1px solid #d9dadc;
  background-color: var(--white);
  padding: 12px 20px;
  width: 100%;
  border-radius: 30px;
  color: var(--text);
}
.header-middle-wrapper .search-toggle-box .input-area .cmn-btn {
  position: absolute;
  top: 16px;
  right: 20px;
  font-size: 16px;
  color: var(--header);
}
.header-middle-wrapper .header-right {
  gap: 20px;
}
.header-middle-wrapper .header-right .menu-cart {
  position: relative;
}
.header-middle-wrapper .header-right .menu-cart .cart-icon {
  position: relative;
  display: inline-block;
  border-radius: 50%;
}
.header-middle-wrapper .header-right .menu-cart .cart-icon::before {
  position: absolute;
  top: -7px;
  right: -12px;
  content: "1";
  width: 18px;
  line-height: 18px;
  height: 18px;
  border-radius: 18px;
  background-color: var(--theme);
  color: var(--white);
  font-size: 12px;
  text-align: center;
  font-weight: 500;
}
.header-middle-wrapper .header-right .menu-cart .cart-icon i {
  color: var(--header);
  font-size: 18px;
}
.header-middle-wrapper .header-right .menu-cart:hover .cart-box {
  transform: rotateX(0deg);
  visibility: visible;
}

.header-4 {
  position: relative;
  z-index: 9;
  background-color: var(--white);
}
.header-4 .header-main {
  justify-content: center;
}
@media (max-width: 1199px) {
  .header-4 .header-main {
    justify-content: space-between;
  }
}
.header-4 .header-logo {
  display: none;
}
@media (max-width: 1199px) {
  .header-4 .header-logo {
    display: block;
  }
}

.sticky {
  position: fixed !important;
  top: 0 !important;
  left: 0;
  width: 100%;
  z-index: 999;
  background-color: var(--white);
  box-shadow: var(---box-shadow);
  transition: all 0.9s;
  -webkit-animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;
  animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;
}
.sticky.header-1.style-3 .header-main .main-menu ul li a {
  color: var(--header);
}
.sticky.header-1.style-3 .header-main .sidebar__toggle {
  color: var(--header);
}
.sticky.header-1.style-3 .header-logo-2 {
  display: none;
}
.sticky.header-1.style-3 .header-logo {
  display: block;
}
.sticky.header-1.style-3 .header-right .search-icon {
  color: var(--header);
}
.sticky.header-1.style-3 .header-right .user-icon {
  color: var(--header);
}
.sticky.header-1.style-3 .header-right .menu-cart .cart-icon i {
  color: var(--header);
}
.sticky.header-2 {
  padding: 0 40px;
}
.sticky.header-2 .logo {
  display: block;
}
.sticky.header-4 .header-main {
  justify-content: space-between;
}
.sticky.header-4 .header-logo {
  display: block;
}

.offcanvas__info {
  background: var(--white) none repeat scroll 0 0;
  border-left: 2px solid var(--theme);
  position: fixed;
  right: 0;
  top: 0;
  width: 400px;
  height: 100%;
  -webkit-transform: translateX(calc(100% + 80px));
  -moz-transform: translateX(calc(100% + 80px));
  -ms-transform: translateX(calc(100% + 80px));
  -o-transform: translateX(calc(100% + 80px));
  transform: translateX(calc(100% + 80px));
  -webkit-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  -moz-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  z-index: 9999;
  overflow-y: scroll;
  overscroll-behavior-y: contain;
  scrollbar-width: none;
}
.offcanvas__info::-webkit-scrollbar {
  display: none;
}

.offcanvas__info.info-open {
  opacity: 1;
  -webkit-transform: translateX(0);
  -moz-transform: translateX(0);
  -ms-transform: translateX(0);
  -o-transform: translateX(0);
  transform: translateX(0);
}

.offcanvas__wrapper {
  position: relative;
  height: 100%;
  padding: 30px 30px;
}
.offcanvas__wrapper .offcanvas__content .text {
  color: var(--text);
}
.offcanvas__wrapper .offcanvas__content .offcanvas__close {
  width: 45px;
  height: 45px;
  line-height: 45px;
  text-align: center;
  border-radius: 50%;
  background-color: var(--theme);
  position: relative;
  z-index: 9;
  cursor: pointer;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__close i {
  color: var(--white);
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact {
  margin-top: 50px;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact ul {
  margin-top: 20px;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact ul li {
  font-size: 16px;
  font-weight: 500;
  text-transform: capitalize;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact ul li:not(:last-child) {
  margin-bottom: 15px;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact ul li .offcanvas__contact-icon {
  margin-right: 20px;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact ul li .offcanvas__contact-icon i {
  color: var(--theme);
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact .header-button .theme-btn {
  width: 100%;
  padding: 20px 40px;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact .social-icon {
  margin-top: 30px;
  gap: 10px;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact .social-icon a {
  width: 45px;
  height: 45px;
  line-height: 45px;
  text-align: center;
  font-size: 16px;
  display: block;
  background: transparent;
  color: var(--header);
  border-radius: 50%;
  -webkit-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
  text-align: center;
  border: 1px solid var(--border);
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact .social-icon a:hover {
  background-color: var(--theme);
  color: var(--white);
}

.offcanvas__overlay {
  position: fixed;
  height: 100%;
  width: 100%;
  background: #151515;
  z-index: 900;
  top: 0;
  opacity: 0;
  visibility: hidden;
  right: 0;
}

.offcanvas__overlay.overlay-open {
  opacity: 0.8;
  visibility: visible;
}

@media (max-width: 450px) {
  .offcanvas__info {
    width: 300px;
  }
}
@media (max-width: 575px) {
  .offcanvas__wrapper {
    padding: 20px;
  }
}
.breadcrumb-wrapper {
  position: relative;
  overflow: hidden;
  z-index: 9;
}
.breadcrumb-wrapper .page-heading {
  position: relative;
  z-index: 99;
  margin-top: -10px;
}
.breadcrumb-wrapper .page-heading h1 {
  font-size: 85px;
  position: relative;
  z-index: 9;
  line-height: 1;
  text-transform: capitalize;
}
@media (max-width: 1199px) {
  .breadcrumb-wrapper .page-heading h1 {
    font-size: 65px;
  }
}
@media (max-width: 767px) {
  .breadcrumb-wrapper .page-heading h1 {
    font-size: 45px;
  }
}
.breadcrumb-wrapper .page-heading .breadcrumb-items {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background-color: var(--white);
  padding: 8px 30px;
  border-radius: 30px;
  margin-top: 30px;
}
.breadcrumb-wrapper .page-heading .breadcrumb-items li {
  font-size: 18px;
  font-weight: 500;
  text-transform: capitalize;
  background: var(--color-gradient-1);
  display: inline-block;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
@media (max-width: 575px) {
  .breadcrumb-wrapper .page-heading .breadcrumb-items li {
    font-size: 16px;
  }
}
.breadcrumb-wrapper .page-heading .breadcrumb-items li a {
  color: var(--theme);
  transition: all 0.4s ease-in-out;
}
.breadcrumb-wrapper .page-heading .breadcrumb-items li a:hover {
  color: var(--theme);
}
.breadcrumb-wrapper .page-heading .breadcrumb-items li i {
  font-size: 18px;
  color: var(--theme);
}
@media (max-width: 575px) {
  .breadcrumb-wrapper .page-heading .breadcrumb-items li i {
    font-size: 16px;
  }
}

.error-content {
  margin-top: -50px;
}
.error-content h2 {
  font-weight: 700;
  font-size: 320px;
  color: var(--theme);
  line-height: 1;
}
.error-content h2 span {
  color: var(--header);
}
@media (max-width: 1199px) {
  .error-content h2 {
    font-size: 300px;
  }
}
@media (max-width: 991px) {
  .error-content h2 {
    font-size: 200px;
  }
}
@media (max-width: 575px) {
  .error-content h2 {
    font-size: 110px;
  }
}
.error-content h3 {
  font-weight: 700;
  font-size: 32px;
}

.side_bar {
  position: fixed;
  top: 0;
  right: 0px;
  width: 420px;
  height: 100%;
  background-color: var(--white);
  padding: 40px;
  padding-top: 25px;
  transition: all 0.3s ease-in-out;
  box-shadow: var(---box-shadow);
  z-index: 9999;
  overflow-y: scroll;
}
@media (max-width: 470px) {
  .side_bar {
    width: 350px;
  }
}
.side_bar .info .icon__item {
  display: flex;
  gap: 20px;
  align-items: center;
}
.side_bar .info .icon__item:not(:last-child) {
  margin-bottom: 30px;
}
.side_bar .info .icon__item .icon {
  color: var(--theme);
  font-size: 32px;
}
.side_bar .info .icon__item .content p {
  margin-bottom: 5px;
}
.side_bar .info .icon__item .content h6 {
  font-size: 16px;
  color: var(--white);
}
.side_bar .x-mark-icon {
  position: absolute;
  right: 40px;
  top: 25px;
  text-align: center;
  font-size: 20px;
  transition: all 0.3s ease-in-out;
  color: var(--header);
}
.side_bar .x-mark-icon:hover {
  transform: rotate(90deg);
}
.side_bar .cartmini__del {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 25px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  color: var(--header);
  font-size: 16px;
}
.side_bar p {
  color: var(--white);
}
.side_bar .cart-title {
  margin-bottom: 20px;
}
.side_bar .cart-title h4 {
  color: var(--header);
}
.side_bar .cartmini__widget {
  height: 100%;
}
.side_bar .cartmini__widget .cartmini__widget-item {
  position: relative;
  display: flex;
  padding: 20px;
  border-bottom: 1px solid rgba(129, 129, 129, 0.2);
  transition: background-color 0.3s;
  gap: 30px;
  padding-left: 0;
  padding-top: 20px;
  align-items: center;
}
.side_bar .cartmini__widget .cartmini__widget-item .cartmini__thumb {
  max-width: 100px;
  height: 100px;
}
.side_bar .cartmini__widget .cartmini__widget-item .cartmini__thumb img {
  width: 100%;
  height: 100%;
}
.side_bar .cartmini__widget .cartmini__widget-item .cartmini__content h5 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
}
.side_bar .cartmini__checkout {
  padding: 20px;
  padding-bottom: 85px;
  width: 100%;
  background: var(--white);
  border-top: 2px solid var(--border);
  margin-top: 150px;
  padding-right: 0;
  padding-left: 0;
}
.side_bar .cartmini__checkout .cartmini__checkout-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.side_bar .cartmini__checkout .cartmini__checkout-title h4 {
  font-size: 18px;
  display: inline-block;
  font-weight: 600;
  margin-bottom: 0;
}
.side_bar .cartmini__checkout .cartmini__checkout-title span {
  font-size: 20px;
  font-weight: 600;
  color: var(--theme);
}
.side_bar .cartmini__checkout-btn .theme-btn {
  text-align: center;
  padding: 20px 60px;
}
.side_bar .cartmini__checkout-btn .theme-btn.style-2 {
  background-color: var(--header);
}
.side_bar .cartmini__checkout-btn .theme-btn.style-2::before, .side_bar .cartmini__checkout-btn .theme-btn.style-2::after {
  background-color: var(--theme);
}

.side_bar_hidden {
  visibility: hidden;
  opacity: 0;
  right: -30px;
}

.mean-container a.meanmenu-reveal {
  display: none;
}

.mean-container .mean-nav {
  background: none;
  margin-top: 0;
}

.mean-container .mean-bar {
  padding: 0;
  min-height: auto;
  background: none;
}

.mean-container .mean-nav > ul {
  padding: 0;
  margin: 0;
  width: 100%;
  list-style-type: none;
  display: block !important;
}
.mean-container .mean-nav > ul .home-menu {
  position: relative;
}
.mean-container .mean-nav > ul .home-menu .home-menu-thumb {
  position: relative;
  width: 280px;
}
.mean-container .mean-nav > ul .home-menu .home-menu-thumb .demo-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60%;
  gap: 10px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease-in-out;
}
.mean-container .mean-nav > ul .home-menu .home-menu-thumb .demo-button .theme-btn {
  padding: 10px 30px;
  color: var(--white) !important;
  width: initial;
  font-size: 14px;
  text-align: center;
}
.mean-container .mean-nav > ul .home-menu .home-menu-thumb .demo-button .theme-btn:hover {
  color: var(--white) !important;
}
.mean-container .mean-nav > ul .home-menu .home-menu-thumb::before {
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(99, 92, 92, 0)), to(#292930));
  background: linear-gradient(to bottom, rgba(99, 92, 92, 0) 0%, #292930 100%);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  overflow: hidden;
  opacity: 0;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  content: "";
}
.mean-container .mean-nav > ul .home-menu .home-menu-thumb:hover::before {
  visibility: visible;
  opacity: 1;
}
.mean-container .mean-nav > ul .home-menu .home-menu-thumb:hover .demo-button {
  opacity: 1;
  visibility: visible;
  margin-top: 0;
}
.mean-container .mean-nav > ul .home-menu .home-menu-thumb:hover .home-menu-btn {
  opacity: 1;
  visibility: visible;
  bottom: 50%;
  transform: translateY(50%);
}
.mean-container .mean-nav > ul .home-menu .home-menu-thumb img {
  width: 100%;
}
.mean-container .mean-nav > ul .home-menu .home-menu-content .home-menu-title {
  margin-top: 15px;
  display: inline-block;
  font-size: 16px;
}

.mean-container a.meanmenu-reveal {
  display: none !important;
}

.mean-container .mean-nav ul li a {
  width: 100%;
  padding: 10px 0;
  color: var(--header);
  font-size: 16px;
  line-height: 1.5;
  font-weight: 600;
}
.mean-container .mean-nav ul li a:hover {
  color: var(--theme2);
}

.mean-container .mean-nav ul li a:last-child {
  border-bottom: 0;
}

.mean-container .mean-nav ul li a:hover {
  color: var(--theme2);
}

.mean-container .mean-nav ul li a.mean-expand {
  margin-top: 5px;
  padding: 0 !important;
}

.mean-container .mean-nav ul li > a > i {
  display: none;
}

.mean-container .mean-nav ul li > a.mean-expand i {
  display: inline-block;
}

.mean-container .mean-nav > ul > li:first-child > a {
  border-top: 0;
}

.mean-container .mean-nav ul li a.mean-expand.mean-clicked i {
  transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transition: all 0.4s ease-in-out;
}

.mean-container .mean-nav ul li .mega-menu li a {
  height: 200px;
  width: 100%;
  padding: 0;
  border-top: 0;
  margin-bottom: 20px;
}

.hero-1 {
  position: relative;
  z-index: 9;
  overflow: hidden;
  padding: 120px 0 120px;
}
.hero-1 .swiper-dot-2 {
  position: absolute;
  top: 50%;
  right: 5%;
  width: 20px;
}
.hero-1 .swiper {
  overflow: initial;
}
.hero-1::before {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  content: "";
  z-index: -1;
  background: linear-gradient(90deg, rgb(73, 81, 254) 24%, rgb(73, 81, 254) 38%, rgb(148, 69, 211) 62%, rgb(223, 57, 167) 100%);
  opacity: 0.4;
}
.hero-1 .hero-content {
  opacity: 0;
  z-index: 3;
  position: relative;
  transform: translateY(-150px);
}
.hero-1 .hero-content h1 {
  color: var(--black);
  margin-bottom: 30px;
}
@media (max-width: 1199px) {
  .hero-1 .hero-content h1 {
    font-size: 60px;
  }
}
@media (max-width: 991px) {
  .hero-1 .hero-content h1 {
    font-size: 68px;
  }
}
@media (max-width: 767px) {
  .hero-1 .hero-content h1 {
    font-size: 54px;
  }
}
@media (max-width: 575px) {
  .hero-1 .hero-content h1 {
    font-size: 42px;
    margin-bottom: 25px;
  }
}
.hero-1 .hero-content p {
  font-weight: 500;
  font-size: 18px;
  color: var(--black);
}
@media (max-width: 575px) {
  .hero-1 .hero-content p {
    font-size: 16px;
  }
}
.hero-1 .hero-button {
  margin-top: 50px;
  transform: translateY(150px);
  opacity: 0;
}
@media (max-width: 767px) {
  .hero-1 .hero-button {
    margin-top: 35px;
  }
}
@media (max-width: 575px) {
  .hero-1 .hero-button {
    margin-top: 25px;
  }
}
.hero-1 .hero-image {
  position: relative;
}
@media (max-width: 991px) {
  .hero-1 .hero-image {
    text-align: center;
  }
}
.hero-1 .hero-image .hero-img {
  position: relative;
  transform: translateY(150px);
  opacity: 0;
}
.hero-1 .hero-image .mockup-shape {
  position: absolute;
  top: 14px;
  left: -32%;
  transform: translateX(-150px);
  opacity: 0;
}
@media (max-width: 991px) {
  .hero-1 .hero-image .mockup-shape {
    left: 0;
  }
}
@media (max-width: 575px) {
  .hero-1 .hero-image .mockup-shape {
    display: none;
  }
}
.hero-1 .hero-image .hero-img {
  position: relative;
}
.hero-1 .hero-image .hero-img .circle-shape {
  position: absolute;
  top: 50%;
  left: 34%;
  transform: translate(-50%, -50%);
  z-index: -1;
}
@media (max-width: 991px) {
  .hero-1 .hero-image .hero-img .circle-shape {
    left: 50%;
  }
}
.hero-1 .hero-image .cup-shape {
  position: absolute;
  bottom: 0;
  left: -100px;
  transform: translateY(150px);
  opacity: 0;
}
@media (max-width: 991px) {
  .hero-1 .hero-image .cup-shape {
    left: 0;
  }
}
.hero-1 .hero-image .t-shirt-shape {
  position: absolute;
  right: 50px;
  top: 30px;
  transform: translateX(150px);
  opacity: 0;
}
.hero-1 .hero-image .cap-shape {
  position: absolute;
  bottom: -15%;
  right: 0;
  transform: translateY(150px);
  opacity: 0;
}
.hero-1 .swiper-slide.swiper-slide-active .hero-content {
  opacity: 1;
  transform: translateY(0px);
  transition: all 1500ms ease;
}
.hero-1 .swiper-slide.swiper-slide-active .hero-button {
  opacity: 1;
  transform: translateY(0px);
  transition: all 2000ms ease;
}
.hero-1 .swiper-slide.swiper-slide-active .hero-image .mockup-shape {
  opacity: 1;
  transform: translateX(0px);
  transition: all 3500ms ease;
}
.hero-1 .swiper-slide.swiper-slide-active .hero-image .cup-shape {
  opacity: 1;
  transform: translateY(0px);
  transition: all 3000ms ease;
}
.hero-1 .swiper-slide.swiper-slide-active .hero-image .cap-shape {
  opacity: 1;
  transform: translateY(0px);
  transition: all 3500ms ease;
}
.hero-1 .swiper-slide.swiper-slide-active .hero-image .hero-img {
  opacity: 1;
  transform: translateY(0px);
  transition: all 1500ms ease;
}
.hero-1 .swiper-slide.swiper-slide-active .hero-image .t-shirt-shape {
  opacity: 1;
  transform: translateX(0px);
  transition: all 2500ms ease;
}

.hero-2 {
  position: relative;
  padding: 185px 0 185px;
}
@media (max-width: 1199px) {
  .hero-2 {
    padding: 150px 0 150px;
  }
}
@media (max-width: 575px) {
  .hero-2 {
    padding: 120px 0 120px;
  }
}
.hero-2 .hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
  -webkit-transform: scale(1);
  transform: scale(1);
  -webkit-transition: opacity 1500ms ease-in, -webkit-transform 7000ms ease;
  transition: opacity 1500ms ease-in, -webkit-transform 7000ms ease;
  transition: transform 9000ms ease, opacity 1500ms ease-in;
  transition: transform 9000ms ease, opacity 1500ms ease-in, -webkit-transform 9000ms ease;
}
.hero-2 .hero-bg::before {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  content: "";
  background-color: rgba(25, 13, 78, 0.6);
  z-index: 1;
}
.hero-2 .hero-content {
  position: relative;
  z-index: 9;
  text-align: center;
  opacity: 0;
  z-index: 3;
  transform: translateY(-150px);
}
.hero-2 .hero-content h6 {
  color: var(--white);
  letter-spacing: 1.2px;
  margin-bottom: 20px;
}
.hero-2 .hero-content h1 {
  color: var(--white);
  font-size: 110px;
  line-height: 1;
}
@media (max-width: 1399px) {
  .hero-2 .hero-content h1 {
    font-size: 80px;
  }
}
@media (max-width: 991px) {
  .hero-2 .hero-content h1 {
    font-size: 70px;
  }
}
@media (max-width: 767px) {
  .hero-2 .hero-content h1 {
    font-size: 55px;
  }
}
@media (max-width: 575px) {
  .hero-2 .hero-content h1 {
    font-size: 42px;
  }
}
.hero-2 .hero-button {
  margin-top: 50px;
  text-align: center;
  transform: translateY(150px);
  opacity: 0;
}
@media (max-width: 767px) {
  .hero-2 .hero-button {
    margin-top: 30px;
  }
}

.hero-section-2 {
  position: relative;
}
.hero-section-2 .swiper-slide.swiper-slide-active .hero-bg {
  -webkit-transform: scale(1.35);
  transform: scale(1.35);
}
.hero-section-2 .swiper-slide.swiper-slide-active .hero-content {
  opacity: 1;
  transform: translateY(0px);
  transition: all 2500ms ease;
}
.hero-section-2 .swiper-slide.swiper-slide-active .hero-button {
  opacity: 1;
  transform: translateY(0px);
  transition: all 2500ms ease;
}
.hero-section-2 .swiper-dot {
  position: absolute;
  bottom: 5%;
  left: 50%;
  z-index: 99;
  transform: translateX(-50%);
}
.hero-section-2 .swiper-dot .swiper-pagination-bullet {
  background-color: var(--white);
}
.hero-section-2 .array-button {
  position: initial;
}
@media (max-width: 1199px) {
  .hero-section-2 .array-button {
    display: none;
  }
}
.hero-section-2 .array-button .array-prev, .hero-section-2 .array-button .array-next {
  position: absolute;
  top: 50%;
  left: 2%;
  z-index: 9;
  background-color: transparent;
  border: 1px solid var(--white);
  color: var(--white);
  width: 70px;
  height: 70px;
  line-height: 70px;
  font-size: 20px;
}
.hero-section-2 .array-button .array-prev:hover, .hero-section-2 .array-button .array-next:hover {
  background-color: var(--theme);
  color: var(--white);
  border-color: var(--theme);
}
.hero-section-2 .array-button .array-next {
  left: auto;
  right: 2%;
}

.hero-3 {
  position: relative;
  padding: 190px 0 110px;
  z-index: 9;
  overflow: hidden;
}
@media (max-width: 991px) {
  .hero-3 {
    padding-top: 150px;
  }
}
.hero-3 .hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  height: 41%;
  width: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
}
.hero-3 .hero-content {
  opacity: 0;
  z-index: 3;
  position: relative;
  transform: translateY(-150px);
}
@media (max-width: 767px) {
  .hero-3 .hero-content {
    text-align: center;
  }
}
.hero-3 .hero-content h6 {
  color: var(--white);
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 15px;
}
.hero-3 .hero-content h1 {
  color: var(--white);
  margin-bottom: 25px;
}
@media (max-width: 991px) {
  .hero-3 .hero-content h1 {
    font-size: 62px;
  }
}
@media (max-width: 767px) {
  .hero-3 .hero-content h1 {
    font-size: 50px;
  }
}
@media (max-width: 575px) {
  .hero-3 .hero-content h1 {
    font-size: 38px;
  }
}
.hero-3 .hero-content p {
  color: var(--white);
  font-weight: 500;
}
.hero-3 .hero-button {
  margin-top: 40px;
  transform: translateY(150px);
  opacity: 0;
}
@media (max-width: 767px) {
  .hero-3 .hero-button {
    text-align: center;
  }
}
.hero-3 .hero-button .theme-btn {
  background-color: var(--white);
  color: var(--header);
}
.hero-3 .hero-button .theme-btn:hover {
  color: var(--white);
}
.hero-3 .hero-image {
  position: relative;
  z-index: 3;
  transform: translateY(150px);
  opacity: 0;
  height: 605px;
}
.hero-3 .hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
@media (max-width: 767px) {
  .hero-3 .hero-image {
    height: initial;
  }
}

.hero-section-3 {
  position: relative;
}
.hero-section-3 .swiper-slide.swiper-slide-active .hero-content {
  opacity: 1;
  transform: translateY(0px);
  transition: all 2500ms ease;
}
.hero-section-3 .swiper-slide.swiper-slide-active .hero-button, .hero-section-3 .swiper-slide.swiper-slide-active .hero-image {
  opacity: 1;
  transform: translateY(0px);
  transition: all 2500ms ease;
}

.hero-4 {
  padding: 140px 0 140px;
}
@media (max-width: 767px) {
  .hero-4 {
    padding: 120px 0;
  }
}
@media (max-width: 767px) {
  .hero-4 .hero-content {
    text-align: center;
  }
}
.hero-4 .hero-content h1 {
  font-weight: 700;
  text-transform: capitalize;
  background: linear-gradient(90.07deg, #FF4E8D 32.43%, #AE34E8 58.79%, #7000fe 105.32%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
@media (max-width: 767px) {
  .hero-4 .hero-content h1 {
    font-size: 55px;
  }
}
@media (max-width: 575px) {
  .hero-4 .hero-content h1 {
    font-size: 40px;
  }
}
.hero-4 .hero-content h1 strong {
  font-weight: 700;
}
.hero-4 .hero-content p {
  font-size: 20px;
  margin-top: 30px;
}
.hero-4 .hero-content .hero-list {
  font-size: 18px;
  font-weight: 500;
  color: var(--header);
  margin-top: 30px;
  margin-bottom: 50px;
}
@media (max-width: 767px) {
  .hero-4 .hero-content .hero-list {
    margin-bottom: 30px;
  }
}
.hero-4 .hero-content .hero-list li:not(:last-child) {
  margin-bottom: 15px;
}
.hero-4 .hero-content .hero-list li i {
  color: var(--theme);
  margin-right: 10px;
}
.hero-4 .hero-content .theme-btn {
  background-color: var(--header);
}
.hero-4 .hero-content .theme-btn::before, .hero-4 .hero-content .theme-btn::after {
  background-color: var(--theme);
}
.hero-4 .hero-thumb img {
  width: 100%;
  height: 100%;
  border-radius: 20px;
}
.hero-4 .hero-thumb.style-2 {
  margin-top: 24px;
}
.hero-4 .hero-thumb.style-3 {
  margin-top: 50px;
}
@media (max-width: 991px) {
  .hero-4 .hero-thumb.style-3 {
    margin-top: 0;
  }
}
.hero-4 .hero-thumb.animation-1 {
  animation: translateX2 4s forwards infinite alternate;
}
.hero-4 .hero-thumb.animation-2 {
  animation: translateY2 4s forwards infinite alternate;
}

.feature-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px dashed #C8CBCD;
  padding: 50px 40px;
}
@media (max-width: 991px) {
  .feature-wrapper {
    flex-wrap: wrap;
    gap: 25px;
  }
}
.feature-wrapper .feature-items {
  display: flex;
  align-items: center;
  gap: 20px;
}
.feature-wrapper .feature-items .content h6 {
  font-weight: 600;
}
.feature-wrapper .feature-items .content p {
  font-size: 15px;
}

.printing-feature-wrapper .printing-image img {
  width: 100%;
  height: 100%;
}
.printing-feature-wrapper .printing-items {
  text-align: center;
}
.printing-feature-wrapper .printing-items .icon {
  width: 110px;
  height: 110px;
  line-height: 100px;
  text-align: center;
  border-radius: 50%;
  background-color: var(--white);
  margin: 0 auto;
}
.printing-feature-wrapper .printing-items .content {
  margin-top: 20px;
}
.printing-feature-wrapper .printing-items .content h3 {
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 20px;
}

.choose-us-wrapper .choose-us-image img {
  width: 100%;
  height: 100%;
}
.choose-us-wrapper .choose-us-content {
  margin-left: 30px;
}
.choose-us-wrapper .choose-us-content .icon-items {
  margin-top: 30px;
  display: flex;
  align-items: center;
  gap: 15px;
}
.choose-us-wrapper .choose-us-content .theme-btn {
  margin-top: 40px;
}

.work-process-card-items {
  margin-top: 30px;
  text-align: center;
}
.work-process-card-items .icon {
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
  border-radius: 50%;
  background: linear-gradient(90deg, rgba(73, 81, 254, 0.112) 24%, rgba(73, 81, 254, 0.112) 38%, rgba(148, 69, 211, 0.112) 62%, rgba(223, 57, 167, 0.112) 100%);
  margin: 0 auto;
  position: relative;
}
.work-process-card-items .content {
  margin-top: 20px;
}
.work-process-card-items .content h3 {
  margin-bottom: 8px;
}

.counter-box-items {
  text-align: center;
}
.counter-box-items .icon {
  width: 120px;
  height: 120px;
  line-height: 120px;
  border-radius: 50%;
  text-align: center;
  margin: 0 auto;
  border: 1px dashed var(--white);
  filter: grayscale(100%) brightness(300%);
}
.counter-box-items .icon img {
  filter: grayscale(100%) brightness(300%);
}
.counter-box-items .content {
  margin-top: 20px;
}
.counter-box-items .content h2 {
  color: var(--white);
  font-size: 60px;
}
.counter-box-items .content p {
  font-size: 18px;
  color: var(--white);
  margin-top: 5px;
}

.how-works-content h2 {
  font-size: 40px;
  margin-top: 30px;
  margin-bottom: 10px;
}
.how-works-content p {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 40px;
}

.how-work-items {
  text-align: center;
  background-color: var(--white);
  padding: 30px 30px;
  box-shadow: var(---box-shadow);
}
.how-work-items .thumb img {
  height: 180px;
  object-fit: cover;
}
.how-work-items .content {
  margin-top: 20px;
}
.how-work-items .content h4 {
  margin-bottom: 10px;
}

.good-quality-wrapper .quality-image img {
  width: 100%;
  height: 100%;
}
@media (max-width: 1199px) {
  .good-quality-wrapper .good-quality-content br {
    display: none;
  }
}
.good-quality-wrapper .good-quality-content .list-item {
  margin-top: 30px;
  display: flex;
  align-items: center;
  gap: 80px;
  margin-bottom: 50px;
}
@media (max-width: 1199px) {
  .good-quality-wrapper .good-quality-content .list-item {
    flex-wrap: wrap;
    gap: 20px;
  }
}
.good-quality-wrapper .good-quality-content .list-item ul li {
  font-weight: 500;
  color: var(--header);
}
.good-quality-wrapper .good-quality-content .list-item ul li i {
  margin-right: 5px;
  color: var(--theme);
}
.good-quality-wrapper .good-quality-content .list-item ul li:not(:last-child) {
  margin-bottom: 12px;
}

.custom-order-wrapper-news .custom-image {
  position: relative;
}
@media (max-width: 991px) {
  .custom-order-wrapper-news .custom-image {
    display: none;
  }
}
.custom-order-wrapper-news .custom-image .premium-logo {
  position: absolute;
  top: 50px;
  left: 0;
}
@media (max-width: 1199px) {
  .custom-order-wrapper-news .custom-image .premium-logo {
    display: none;
  }
}
.custom-order-wrapper-news .icon-box-area {
  background-color: var(--theme);
  padding: 40px;
  margin-left: -250px;
  position: relative;
  z-index: 9;
  margin-top: 40px;
}
@media (max-width: 991px) {
  .custom-order-wrapper-news .icon-box-area {
    margin-left: 0;
  }
}
.custom-order-wrapper-news .icon-box-area .icon-items {
  display: flex;
  gap: 15px;
}
.custom-order-wrapper-news .icon-box-area .icon-items .icon {
  width: 55px;
  height: 55px;
  line-height: 55px;
  background-color: var(--white);
  text-align: center;
}
.custom-order-wrapper-news .icon-box-area .icon-items .content {
  flex-basis: 80%;
}
.custom-order-wrapper-news .icon-box-area .icon-items .content h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--white);
  margin-bottom: 10px;
}
.custom-order-wrapper-news .icon-box-area .icon-items .content p {
  color: rgba(255, 255, 255, 0.85);
}

.about-wrapper {
  position: relative;
  z-index: 9;
}
.about-wrapper .about-image-items {
  position: relative;
}
.about-wrapper .about-image-items .circle-shape {
  position: absolute;
  top: 31%;
  left: 36%;
  animation: cir36 10s linear infinite;
}
.about-wrapper .about-image-items .about-image img {
  width: 100%;
  height: 100%;
}
.about-wrapper .about-image-items .thumb-text {
  position: absolute;
  bottom: 6%;
  left: 17%;
  background-color: var(--white);
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.04);
  border-radius: 4px;
  transform: rotate(-20.23deg);
  padding: 13px 22px;
}
.about-wrapper .about-image-items .thumb-text span {
  font-size: 18px;
  font-weight: 500;
  color: var(--header);
}
@media (max-width: 991px) {
  .about-wrapper .about-image-items .thumb-text span {
    font-size: 15px;
  }
}
@media (max-width: 470px) {
  .about-wrapper .about-image-items .thumb-text span {
    font-size: 14px;
  }
}
.about-wrapper .about-image-items .thumb-text span b {
  color: var(--theme);
  font-size: 25px;
}
@media (max-width: 991px) {
  .about-wrapper .about-image-items .thumb-text span b {
    font-size: 19px;
  }
}
@media (max-width: 470px) {
  .about-wrapper .about-image-items .thumb-text span b {
    font-size: 15px;
  }
}
.about-wrapper .about-new-items .thumb-image-1 img {
  width: 100%;
  height: 100%;
}
.about-wrapper .about-new-items .thumb-image-1.style-2 {
  margin-top: 24px;
}
.about-wrapper .about-new-items .thumb-image-1.style-3 {
  margin-bottom: -22px;
  position: relative;
}
@media (max-width: 1199px) {
  .about-wrapper .about-new-items .thumb-image-1.style-3 {
    margin-bottom: 24px;
  }
}
.about-wrapper .about-new-items .thumb-image-1.style-4 {
  margin-left: -45px;
}
@media (max-width: 1199px) {
  .about-wrapper .about-new-items .thumb-image-1.style-4 {
    margin-left: 0;
  }
}
.about-wrapper .about-content {
  max-width: 550px;
}
.about-wrapper .about-content .progress-wrap {
  margin-top: 40px;
}
.about-wrapper .about-content .progress-wrap .pro-items:not(:last-child) {
  margin-bottom: 30px;
}
.about-wrapper .about-content .progress-wrap .pro-items .pro-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}
.about-wrapper .about-content .progress-wrap .pro-items .pro-head .title {
  font-size: 20px;
  font-weight: 600;
  color: var(--header);
}
.about-wrapper .about-content .progress-wrap .pro-items .pro-head .point {
  font-size: 16px;
  font-weight: 600;
  color: var(--header);
}
.about-wrapper .about-content .progress-wrap .pro-items .progress {
  background: #E6E6E6;
  justify-content: flex-start;
  border-radius: 100px;
  align-items: center;
  position: relative;
  display: flex;
  height: 10px;
  width: 100%;
  border-radius: 0;
}
.about-wrapper .about-content .progress-wrap .pro-items .progress-value {
  animation: load 3s normal forwards;
  border-radius: 0;
  background: var(--theme);
  height: 10px;
  width: 0;
  border-radius: 0;
}
.about-wrapper .about-content .progress-wrap .pro-items .style-two {
  animation: load2 3s normal forwards;
}
.about-wrapper .about-content .progress-wrap .pro-items .style-three {
  animation: load3 3s normal forwards;
}
.about-wrapper .about-content .progress-wrap .pro-items .style-four {
  animation: load4 3s normal forwards;
}
@keyframes load {
  0% {
    width: 0;
  }
  100% {
    width: 80%;
  }
}
@keyframes load2 {
  0% {
    width: 0;
  }
  100% {
    width: 70%;
  }
}
@keyframes load3 {
  0% {
    width: 0;
  }
  100% {
    width: 85%;
  }
}
@keyframes load4 {
  0% {
    width: 0;
  }
  100% {
    width: 90%;
  }
}
.about-wrapper .about-content .icon-box-items {
  margin-top: 30px;
}
.about-wrapper .about-content .icon-box-items .icon-items {
  display: flex;
  align-items: center;
  gap: 20px;
}
.about-wrapper .about-content .icon-box-items .icon-items:not(:last-child) {
  margin-bottom: 30px;
}
.about-wrapper .about-content .icon-box-items .icon-items .icon {
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  display: inline-block;
  background: #f5f7fa;
  color: var(--theme);
  border-radius: 50%;
  font-size: 34px;
  position: relative;
  z-index: 1;
  transition: all 0.4s ease-in-out;
}
.about-wrapper .about-content .icon-box-items .icon-items .icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  transform: scale(0);
  background: var(--theme);
  width: 100%;
  height: 100%;
  z-index: -1;
  transition: all 0.4s ease-in-out;
  border-radius: 50%;
}
.about-wrapper .about-content .icon-box-items .icon-items .content {
  flex-basis: 70%;
}
.about-wrapper .about-content .icon-box-items .icon-items .content p {
  margin-top: 5px;
}
.about-wrapper .about-content .icon-box-items .icon-items:hover .icon {
  color: var(--white);
}
.about-wrapper .about-content .icon-box-items .icon-items:hover .icon::before {
  transform: scale(1);
}
.about-wrapper .about-content .about-author {
  display: flex;
  align-items: center;
  gap: 60px;
  margin-top: 50px;
}
@media (max-width: 1199px) {
  .about-wrapper .about-content .about-author {
    flex-wrap: wrap;
    gap: 20px;
  }
}
@media (max-width: 767px) {
  .about-wrapper .about-content .about-author {
    margin-top: 35px;
  }
}
@media (max-width: 575px) {
  .about-wrapper .about-content .about-author {
    margin-top: 25px;
  }
}
.about-wrapper .about-content .about-author .author-image {
  display: flex;
  align-items: center;
  gap: 15px;
}
.about-wrapper .about-content .about-author .author-image .content p {
  font-size: 14px;
  color: var(--text);
  font-weight: 500;
}

.about-section {
  position: relative;
}
.about-section .dot-shape {
  position: absolute;
  top: 0;
  left: 3%;
  animation: moving 9s linear infinite;
}
.about-section .bag-shape {
  position: absolute;
  top: 30%;
  left: 5%;
  animation: translateY2 2s forwards infinite alternate;
}
.about-section .shape-1 {
  position: absolute;
  top: 20%;
  right: 0;
}
@media (max-width: 1600px) {
  .about-section .shape-1 {
    display: none;
  }
}

.about-wrapper-2 .about-image-items {
  position: relative;
}
.about-wrapper-2 .about-image-items .about-image {
  position: relative;
  text-align: center;
}
.about-wrapper-2 .about-image-items .about-image::before {
  position: absolute;
  top: 0;
  left: 0;
  content: "";
  width: 222px;
  height: 222px;
  border-radius: 50%;
  opacity: 0.2;
  background: linear-gradient(180deg, #6F32F0 0%, #FF47EE 100%);
  z-index: -1;
}
@media (max-width: 1199px) {
  .about-wrapper-2 .about-image-items .about-image::before {
    width: 480px;
    height: 480px;
  }
}
@media (max-width: 991px) {
  .about-wrapper-2 .about-image-items .about-image::before {
    width: 525px;
    height: 525px;
  }
}
@media (max-width: 575px) {
  .about-wrapper-2 .about-image-items .about-image::before {
    width: 300px;
    height: 300px;
  }
}
@media (max-width: 575px) {
  .about-wrapper-2 .about-image-items .about-image img {
    width: 100%;
    height: 100%;
  }
}
.about-wrapper-2 .about-image-items .shape-1 {
  position: absolute;
  top: 10%;
  left: 0;
  animation: moving 9s linear infinite;
}
@media (max-width: 470px) {
  .about-wrapper-2 .about-image-items .shape-1 {
    display: none;
  }
}
.about-wrapper-2 .about-image-items .shape-2 {
  position: absolute;
  bottom: 0;
  left: 0;
  animation: translateX2 2s forwards infinite alternate;
}
.about-wrapper-2 .about-image-items .shape-3 {
  position: absolute;
  top: 60px;
  right: 70px;
  animation: translateX2 2s forwards infinite alternate;
}
.about-wrapper-2 .about-image-items .shape-4 {
  position: absolute;
  bottom: 12px;
  right: 20px;
  animation: translateY2 2s forwards infinite alternate;
}
@media (max-width: 470px) {
  .about-wrapper-2 .about-image-items .shape-4 {
    display: none;
  }
}
.about-wrapper-2 .about-content {
  max-width: 600px;
}
.about-wrapper-2 .about-content .icon-items {
  display: flex;
  gap: 20px;
}
.about-wrapper-2 .about-content .icon-items .content {
  max-width: 260px;
}
.about-wrapper-2 .about-content .icon-items .content h4 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 5px;
}
.about-wrapper-2 .about-content p {
  padding-top: 10px;
  font-weight: 400;
}
.about-wrapper-2 .about-content .about-icon-area {
  margin-top: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 1199px) {
  .about-wrapper-2 .about-content .about-icon-area {
    flex-wrap: wrap;
    gap: 30px;
  }
}
@media (max-width: 575px) {
  .about-wrapper-2 .about-content .about-icon-area {
    margin-top: 30px;
  }
}
.about-wrapper-2 .about-content .about-list li {
  font-size: 18px;
  font-weight: 400;
}
@media (max-width: 575px) {
  .about-wrapper-2 .about-content .about-list li {
    font-size: 16px;
  }
}
.about-wrapper-2 .about-content .about-list li:not(:last-child) {
  margin-bottom: 20px;
}
.about-wrapper-2 .about-content .about-list li i {
  color: var(--theme);
  margin-right: 10px;
}
.about-wrapper-2 .about-content .about-author {
  display: flex;
  align-items: center;
  gap: 30px;
  margin-top: 55px;
}
@media (max-width: 575px) {
  .about-wrapper-2 .about-content .about-author {
    margin-top: 30px;
  }
}
@media (max-width: 1399px) {
  .about-wrapper-2 .about-content .about-author {
    gap: 40px;
  }
}
@media (max-width: 1199px) {
  .about-wrapper-2 .about-content .about-author {
    flex-wrap: wrap;
  }
}
.about-wrapper-2 .about-content .about-author .author-image {
  display: flex;
  align-items: center;
  gap: 20px;
}
.about-wrapper-2 .about-content .about-author .author-image .info-content span {
  color: var(--text);
  position: relative;
}
.about-wrapper-2 .about-content .about-author .author-image .info-content span img {
  position: absolute;
  bottom: -10px;
  left: 4px;
}
.about-wrapper-2 .about-content .about-author .author-image .info-content h3 {
  font-weight: 600;
}

.about-wrapper-3 .about-image-3 {
  margin-left: -50%;
  margin-bottom: -120px;
}
@media (max-width: 1199px) {
  .about-wrapper-3 .about-image-3 {
    margin-left: 0;
    margin-bottom: 0;
  }
}
.about-wrapper-3 .about-image-3 img {
  width: 100%;
  height: 100%;
}
.about-wrapper-3 .about-content {
  margin-left: 30px;
}
@media (max-width: 1199px) {
  .about-wrapper-3 .about-content {
    margin-left: 0;
  }
}
.about-wrapper-3 .about-content .video-items {
  display: flex;
  align-items: center;
  gap: 30px;
  margin-top: 40px;
}
@media (max-width: 1199px) {
  .about-wrapper-3 .about-content .video-items {
    flex-wrap: wrap;
  }
}
.about-wrapper-3 .about-content .video-items .video-thumb {
  position: relative;
}
.about-wrapper-3 .about-content .video-items .video-thumb .video-box {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.about-wrapper-3 .about-content .video-items .video-thumb .video-box .video-btn {
  background-color: var(--theme);
  color: var(--white);
  display: inline-block;
  font-size: 16px;
  height: 70px;
  width: 70px;
  line-height: 70px;
  border-radius: 50%;
  text-align: center;
}
.about-wrapper-3 .about-content .video-items .video-thumb .ripple::before, .about-wrapper-3 .about-content .video-items .video-thumb .ripple::after {
  width: 70px;
  height: 70px;
  box-shadow: 0 0 0 0 rgba(112, 0, 254, 0.5);
}
.about-wrapper-3 .about-content .video-items .list li {
  font-size: 18px;
  color: var(--header);
}
.about-wrapper-3 .about-content .video-items .list li:not(:last-child) {
  margin-bottom: 10px;
}
.about-wrapper-3 .about-content .video-items .list li i {
  color: var(--theme);
  margin-right: 10px;
}
.about-wrapper-3 .about-content .about-author {
  display: flex;
  align-items: center;
  gap: 60px;
  margin-top: 50px;
}
@media (max-width: 1399px) {
  .about-wrapper-3 .about-content .about-author {
    flex-wrap: wrap;
    gap: 20px;
  }
}
@media (max-width: 767px) {
  .about-wrapper-3 .about-content .about-author {
    margin-top: 35px;
  }
}
@media (max-width: 575px) {
  .about-wrapper-3 .about-content .about-author {
    margin-top: 25px;
  }
}
.about-wrapper-3 .about-content .about-author .author-image {
  display: flex;
  align-items: center;
  gap: 15px;
}
.about-wrapper-3 .about-content .about-author .author-image .content p {
  font-size: 14px;
  color: var(--text);
  font-weight: 500;
}

.shop-banner-items {
  background: var(--theme);
  padding: 15px 0 0 50px;
  overflow: hidden;
}
.shop-banner-items .shop-banner-content {
  position: relative;
  z-index: 9;
}
.shop-banner-items .shop-banner-content span {
  text-transform: uppercase;
  color: var(--white);
  font-weight: 500;
  margin-bottom: 5px;
  display: inline-block;
}
.shop-banner-items .shop-banner-content h3 {
  font-size: 36px;
  color: var(--white);
  margin-bottom: 25px;
}
.shop-banner-items .shop-banner-content .theme-btn {
  background-color: transparent;
  color: var(--white);
  border: 1px solid var(--white);
  padding: 20px 40px;
}
.shop-banner-items .shop-banner-content .theme-btn:hover {
  border: 1px solid var(--header);
}
.shop-banner-items .product-right-wrapper {
  text-align: right;
}
.shop-banner-items .product-right-wrapper .product-right-img {
  position: relative;
  z-index: 1;
  transition: all 0.4s ease-in-out;
  overflow: hidden;
}
.shop-banner-items .product-right-wrapper .product-right-img .product-right-img-shape {
  position: absolute;
  bottom: -121px;
  right: -104px;
  z-index: -1;
}
.shop-banner-items .product-right-wrapper .product-right-img-2 {
  position: relative;
  z-index: 2;
  transition: all 0.4s ease-in-out;
}
.shop-banner-items .product-right-wrapper .product-right-img-2 .product-right-img-shape-2 {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: -1;
}
.shop-banner-items:hover .product-right-img {
  transform: translateX(15px);
}
.shop-banner-items:hover .product-right-img-2 {
  transform: translateX(15px);
}
.shop-banner-items.bg-2 {
  background: linear-gradient(90deg, #4971f9 0%, #2fabf7 100%);
}
.shop-banner-items.style-2 {
  padding: 50px 50px;
}
.shop-banner-items.style-2 .shop-banner-content h3 {
  font-size: 50px;
  margin-bottom: 30px;
}
.shop-banner-items.style-3 {
  background: #1a84eb;
}
.shop-banner-items.style-4 {
  background: #fec931;
}

.shop-banner-section {
  position: relative;
  z-index: 2;
}
.shop-banner-section .container-fluid {
  padding: 0 250px;
}
@media (max-width: 1899px) {
  .shop-banner-section .container-fluid {
    padding: 0 60px;
  }
}
@media (max-width: 1399px) {
  .shop-banner-section .container-fluid {
    padding: 0 40px;
  }
}
@media (max-width: 1199px) {
  .shop-banner-section .container-fluid {
    padding: 0 25px;
  }
}
.shop-banner-section::before {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  content: "";
  background-color: var(--bg);
  z-index: -1;
  top: 40%;
}
.shop-banner-section.style-2::before {
  display: none;
}

.catagories-section {
  position: relative;
}
.catagories-section .bg-shape {
  position: absolute;
  top: 0;
  left: 0;
}

.catagories-card-items {
  margin-top: 30px;
  padding: 0 30px 50px;
  gap: 20px;
  position: relative;
  text-align: center;
  z-index: 9;
}
.catagories-card-items::before {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  content: "";
  background-color: var(--white);
  z-index: -1;
  top: 70px;
  height: initial;
}
.catagories-card-items .content {
  margin-top: 20px;
}
.catagories-card-items .content span {
  background: linear-gradient(90deg, #FF4E8D 0%, #7807FB 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 14px;
  text-transform: capitalize;
  display: inline-block;
  margin-bottom: 5px;
}
.catagories-card-items .content h3 {
  font-weight: 600;
  margin-bottom: 10px;
}
.catagories-card-items .content h3 a:hover {
  color: var(--theme);
}
.catagories-card-items .content .arrow-icon {
  display: inline-block;
  width: 46px;
  height: 46px;
  line-height: 46px;
  text-align: center;
  border-radius: 50%;
  border: 1px solid var(--theme);
  color: var(--theme);
  margin-top: 20px;
}
.catagories-card-items .content .arrow-icon:hover {
  background-color: var(--theme);
  color: var(--white);
}
.catagories-card-items .content .arrow-icon i {
  transform: rotate(-45deg);
}

.feature-product-items {
  padding: 35px 25px;
  border-radius: 12px;
  background-color: var(--white);
  box-shadow: 0px 26px 81px 0px rgba(0, 0, 0, 0.09);
  margin-top: 30px;
  position: relative;
}
.feature-product-items .product-thumb {
  position: relative;
  overflow: hidden;
}
.feature-product-items .product-thumb img {
  width: 100%;
  height: 100%;
  transition: all 0.4s ease-in-out;
}
.feature-product-items .product-thumb .product-icon {
  gap: 16px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.feature-product-items .product-thumb .product-icon li {
  -webkit-transform: scaleX(0);
  transform: scaleX(0);
  -webkit-transition: 0.4s ease-in-out;
  transition: 0.4s ease-in-out;
  cursor: pointer;
  background: var(--header);
  width: 45px;
  height: 45px;
  line-height: 45px;
  text-align: center;
  border-radius: 50%;
}
.feature-product-items .product-thumb .product-icon li:hover {
  background: var(--theme);
}
.feature-product-items .product-thumb .product-icon li:hover i {
  color: var(--white);
}
.feature-product-items .product-thumb .product-icon li a i {
  color: var(--white);
  font-size: 16px;
  transition: all 0.4s ease-in-out;
}
.feature-product-items .product-content h4 a:hover {
  color: var(--theme);
}
.feature-product-items .product-content .price-list {
  margin-top: 5px;
  display: flex;
  align-items: center;
  gap: 7px;
}
.feature-product-items .product-content .price-list li {
  font-weight: 600;
  color: var(--header);
}
.feature-product-items .post-cat {
  background: var(--color-gradient-1);
  position: absolute;
  right: 20px;
  top: 25px;
  text-align: center;
  color: var(--white);
  padding: 6px 14px;
  line-height: 1;
  text-transform: capitalize;
  border-radius: 30px;
  font-size: 13px;
}
.feature-product-items:hover .product-thumb img {
  transform: scale(1.1);
}
.feature-product-items:hover .product-thumb .product-icon li {
  -webkit-transform: scaleX(1);
  transform: scaleX(1);
}

.feature-product-banner-items {
  margin-top: 30px;
  position: relative;
}
.feature-product-banner-items .array-button {
  position: absolute;
  right: 30px;
  top: 30px;
  z-index: 99;
}
.feature-product-banner-items .array-button .array-prev, .feature-product-banner-items .array-button .array-next {
  width: 45px;
  height: 45px;
  line-height: 45px;
  text-align: center;
  border-radius: 50%;
}
.feature-product-banner-items .array-button .array-prev {
  background: var(--white);
  color: var(--header);
  transition: all 0.4s ease-in-out;
}
.feature-product-banner-items .array-button .array-prev:hover {
  background: var(--color-gradient-1);
  color: var(--white);
}
.feature-product-banner-items .array-button .array-next {
  background: var(--color-gradient-1);
  color: var(--white);
  transition: all 0.4s ease-in-out;
}
.feature-product-banner-items .array-button .array-next:hover {
  background: var(--white);
  color: var(--header);
}
.feature-product-banner-items .feature-product-banner-image {
  position: relative;
}
.feature-product-banner-items .feature-product-banner-image img {
  border-radius: 12px;
  width: 100%;
  height: 100%;
}
.feature-product-banner-items .feature-product-banner-image .discount-tag {
  position: absolute;
  top: 30px;
  left: 30px;
  width: 80px;
  color: var(--white);
  height: 80px;
  border-radius: 100%;
  background-color: var(--theme);
}
.feature-product-banner-items .feature-product-banner-image .discount-tag .inner-text {
  margin-top: 10px;
}
.feature-product-banner-items .feature-product-banner-image .discount-tag span {
  line-height: 1;
  font-size: 12px;
  text-transform: uppercase;
}
.feature-product-banner-items .feature-product-banner-image .discount-tag h3 {
  line-height: 1;
  font-size: 19px;
  font-weight: 700;
  color: var(--white);
}
.feature-product-banner-items .feature-product-banner-image .content {
  position: absolute;
  bottom: 30px;
  left: 30px;
}
.feature-product-banner-items .feature-product-banner-image .content h3 {
  font-size: 32px;
}
.feature-product-banner-items .feature-product-banner-image .content h3 a {
  color: var(--white);
}
.feature-product-banner-items .feature-product-banner-image .content h3 a:hover {
  color: var(--theme);
}
.feature-product-banner-items .feature-product-banner-image .content h4 {
  color: var(--white);
  margin-top: 5px;
}

.product-section {
  margin-bottom: -7px;
}

.products-box-items {
  margin-top: 30px;
  background-color: var(--white);
  padding: 15px 22px 15px;
  box-shadow: var(---box-shadow);
  margin-bottom: 40px;
}
.products-box-items .product-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}
.products-box-items .product-top li {
  font-weight: 600;
  color: var(--header);
  font-size: 18px;
}
.products-box-items .product-top li:nth-child(2) {
  color: var(--theme);
}
.products-box-items .product-thumb {
  overflow: hidden;
  position: relative;
}
.products-box-items .product-thumb img {
  width: 100%;
  height: 100%;
}
.products-box-items .product-thumb .product-icon {
  gap: 16px;
  position: absolute;
  top: 30%;
  transform: translateY(-50%);
  right: 7%;
}
.products-box-items .product-thumb .product-icon li {
  -webkit-transform: scaleX(0);
  transform: scaleX(0);
  -webkit-transition: 0.4s ease-in-out;
  transition: 0.4s ease-in-out;
  cursor: pointer;
  background-color: var(--white);
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  color: var(--header);
}
.products-box-items .product-thumb .product-icon li:hover {
  background-color: var(--theme);
}
.products-box-items .product-thumb .product-icon li:hover i {
  color: var(--white);
}
.products-box-items .product-thumb .product-icon li a i {
  color: var(--header);
  font-size: 16px;
  transition: all 0.4s ease-in-out;
}
.products-box-items .product-thumb .theme-btn {
  padding: 18px 36px;
  position: absolute;
  left: 30px;
  text-align: center;
  bottom: -100px;
  right: 30px;
}
.products-box-items .product-content {
  text-align: center;
  margin-top: 20px;
}
.products-box-items .product-content h4 {
  font-weight: 600;
  margin-bottom: 5px;
}
.products-box-items .product-content h4 a:hover {
  color: var(--theme);
}
.products-box-items .product-content .star {
  color: #ecb332;
  font-size: 14px;
}
.products-box-items:hover .product-thumb .product-icon li {
  -webkit-transform: scaleX(1);
  transform: scaleX(1);
}
.products-box-items:hover .product-thumb .theme-btn {
  bottom: 30px;
}

.shop-banner-box-items .content {
  padding: 50px 40px;
}
.shop-banner-box-items .content h2 {
  color: var(--white);
  margin-bottom: 70px;
  font-size: 30px;
}
.shop-banner-box-items .content .theme-btn {
  padding: 19px 35px;
  background-color: var(--white);
  color: var(--header);
}
.shop-banner-box-items .content .theme-btn:hover {
  color: var(--white);
}

.shop-items {
  text-align: center;
}
.shop-items .shop-image {
  position: relative;
}
.shop-items .shop-image img {
  width: 100%;
  height: 100%;
}
.shop-items .shop-image .product-icon {
  gap: 8px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 7%;
}
.shop-items .shop-image .product-icon li {
  -webkit-transform: scaleX(0);
  transform: scaleX(0);
  -webkit-transition: 0.4s ease-in-out;
  transition: 0.4s ease-in-out;
  cursor: pointer;
  background-color: var(--theme);
  width: 40px;
  height: 40px;
  line-height: 40px;
  border-radius: 40px;
  text-align: center;
}
.shop-items .shop-image .product-icon li:hover {
  background-color: var(--header);
}
.shop-items .shop-image .product-icon li:hover i {
  color: var(--white);
}
.shop-items .shop-image .product-icon li a i {
  color: var(--white);
  font-size: 16px;
  transition: all 0.4s ease-in-out;
}
.shop-items .shop-image .offer-text {
  font-size: 14px;
  letter-spacing: 1px;
  background: var(--color-gradient-1);
  display: inline-block;
  font-weight: 600;
  color: var(--white);
  padding: 0 8px;
  margin-bottom: 20px;
  text-transform: uppercase;
  position: absolute;
  top: 15px;
  left: 15px;
}
.shop-items .shop-content {
  margin-top: 20px;
}
.shop-items .shop-content .price-list {
  margin-top: 10px;
}
.shop-items .shop-content .price-list li {
  font-weight: 600;
  color: var(--header);
}
.shop-items .shop-content h5 {
  font-size: 20px;
}
.shop-items .shop-content h5 a:hover {
  color: var(--theme);
}
.shop-items:hover .product-icon li {
  -webkit-transform: scaleX(1);
  transform: scaleX(1);
}
.shop-items.style-2 {
  margin-top: 30px;
}

.shop-banner-image-item {
  height: 720px;
}
@media (max-width: 991px) {
  .shop-banner-image-item {
    height: 630px;
  }
}
@media (max-width: 767px) {
  .shop-banner-image-item {
    height: 530px;
  }
}
@media (max-width: 575px) {
  .shop-banner-image-item {
    height: 430px;
  }
}
.shop-banner-image-item .content {
  padding: 40px;
}
.shop-banner-image-item .content .offer-text {
  font-size: 14px;
  letter-spacing: 1px;
  background: var(--color-gradient-1);
  display: inline-block;
  font-weight: 600;
  color: var(--white);
  padding: 0 8px;
  margin-bottom: 20px;
  text-transform: uppercase;
}
.shop-banner-image-item .content h2 {
  color: var(--header);
}
.shop-banner-image-item .content .theme-btn {
  margin-top: 40px;
  padding: 20px 50px;
  background-color: var(--white);
  color: var(--header);
}
.shop-banner-image-item .content .theme-btn:hover {
  color: var(--white);
}

.catagories-box-items {
  margin-top: 30px;
  background-color: var(--white);
  padding: 35px 28px;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}
.catagories-box-items .content h3 {
  margin-bottom: 5px;
}
.catagories-box-items .content h3 a:hover {
  color: var(--theme);
}
.catagories-box-items .content .theme-btn {
  padding: 16px 25px;
  margin-top: 40px;
  background-color: transparent;
  border: 1px solid var(--border);
  color: var(--header);
}
.catagories-box-items .content .theme-btn::before, .catagories-box-items .content .theme-btn::after {
  background-color: var(--theme);
}
.catagories-box-items .content .theme-btn:hover {
  color: var(--white);
}

.custom-content p {
  font-size: 18px;
  font-weight: 500;
}
.custom-content .list {
  margin-top: 30px;
  margin-bottom: 40px;
}
.custom-content .list li {
  font-size: 20px;
  font-weight: 500;
  color: var(--header);
}
.custom-content .list li:not(:last-child) {
  margin-bottom: 20px;
}
.custom-content .list li i {
  color: var(--theme);
  margin-right: 5px;
}

.custom-image {
  margin-bottom: -120px;
  position: relative;
}
.custom-image img {
  width: 100%;
  height: 100%;
}
.custom-image .information-shape {
  position: absolute;
  top: -120px;
  right: -120px;
}
@media (max-width: 1899px) {
  .custom-image .information-shape {
    display: none;
  }
}

.shop-catagories-wrapper-3 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.shop-catagories-item {
  text-align: center;
}
.shop-catagories-item .content {
  margin-top: 20px;
}
.shop-catagories-item .content h3 {
  font-size: 20px;
}
.shop-catagories-item .content h3 a:hover {
  color: var(--theme);
}
.shop-catagories-item.style-2 {
  margin-top: 30px;
}

.product-header {
  position: relative;
  z-index: 9;
}
.product-header .nav {
  justify-content: center;
  margin-bottom: 30px;
  gap: 35px;
}
.product-header .nav .nav-item .nav-link {
  font-size: 16px;
  font-weight: 700;
  color: var(--header);
  padding: 18px 30px;
  background-color: var(--white);
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.07);
  line-height: 1;
  text-transform: capitalize;
}
.product-header .nav .nav-item .nav-link.active {
  background-color: var(--theme);
  color: var(--white);
}

.popular-product-section {
  margin-top: -10px;
  position: relative;
}
.popular-product-section .array-button {
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease-in-out;
}
.popular-product-section .array-button .array-prev {
  position: absolute;
  left: 12%;
  top: 38%;
  transform: translateY(-50%);
}
.popular-product-section .array-button .array-next {
  position: absolute;
  right: 12%;
  top: 38%;
  transform: translateY(-50%);
}
.popular-product-section:hover .array-button {
  opacity: 1;
  visibility: visible;
}

.trending-product-items {
  margin-top: 30px;
}
.trending-product-items .trending-thumb {
  position: relative;
}
.trending-product-items .trending-thumb img {
  width: 100%;
  height: 100%;
}
.trending-product-items .trending-thumb .post-cat {
  background: var(--color-gradient-1);
  position: absolute;
  left: 20px;
  top: 20px;
  text-align: center;
  color: var(--white);
  padding: 6px 10px;
  line-height: 1;
  text-transform: capitalize;
  font-size: 13px;
}
.trending-product-items .trending-thumb .product-icon {
  gap: 16px;
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
}
.trending-product-items .trending-thumb .product-icon li {
  -webkit-transform: scaleX(0);
  transform: scaleX(0);
  -webkit-transition: 0.4s ease-in-out;
  transition: 0.4s ease-in-out;
  cursor: pointer;
  background: var(--header);
  width: 45px;
  height: 45px;
  line-height: 45px;
  text-align: center;
  border-radius: 50%;
}
.trending-product-items .trending-thumb .product-icon li:hover {
  background: var(--theme);
}
.trending-product-items .trending-thumb .product-icon li:hover i {
  color: var(--white);
}
.trending-product-items .trending-thumb .product-icon li a i {
  color: var(--white);
  font-size: 16px;
  transition: all 0.4s ease-in-out;
}
.trending-product-items .trending-content {
  margin-top: 20px;
}
.trending-product-items .trending-content .price-list {
  margin-bottom: 10px;
}
.trending-product-items .trending-content .price-list li {
  color: var(--header);
}
.trending-product-items .trending-content .price-list li del {
  color: var(--theme);
}
.trending-product-items .trending-content h3 {
  font-size: 20px;
  margin-bottom: 10px;
}
.trending-product-items .trending-content h3 a:hover {
  color: var(--theme);
}
.trending-product-items .trending-content .star {
  font-size: 15px;
  font-weight: 500;
}
.trending-product-items .trending-content .star i {
  color: #FFA800;
}
.trending-product-items:hover .trending-thumb .product-icon li {
  -webkit-transform: scaleX(1);
  transform: scaleX(1);
}

.product-header-2 {
  position: relative;
  z-index: 9;
}
.product-header-2 .nav {
  justify-content: center;
  margin-bottom: 20px;
  gap: 35px;
}
.product-header-2 .nav .nav-item .nav-link {
  font-size: 16px;
  font-weight: 700;
  color: var(--header);
  line-height: 1;
  text-transform: capitalize;
}
.product-header-2 .nav .nav-item .nav-link.active {
  color: var(--theme);
}

.top-ratting-items h4 {
  margin-bottom: 30px;
  font-size: 22px;
}
.top-ratting-items .top-shop-items {
  display: flex;
  align-items: center;
  gap: 15px;
}
.top-ratting-items .top-shop-items:not(:last-child) {
  margin-bottom: 20px;
}
.top-ratting-items .top-shop-items .thumb {
  width: 100px;
  height: 100px;
}
.top-ratting-items .top-shop-items .thumb img {
  width: 100%;
  height: 100%;
}
.top-ratting-items .top-shop-items .content h6 a:hover {
  color: var(--theme);
}
.top-ratting-items .top-shop-items .content .star {
  color: #ecb332;
  font-size: 12px;
}
.top-ratting-items .top-shop-items .content .price-list {
  display: flex;
  align-items: center;
  gap: 7px;
}
.top-ratting-items .top-shop-items .content .price-list li {
  font-weight: 600;
  color: var(--header);
  font-size: 14px;
}

.shop-main-sidebar .single-sidebar-widget {
  background-color: var(--white);
  box-shadow: var(---box-shadow);
  padding: 40px 30px;
}
.shop-main-sidebar .single-sidebar-widget:not(:last-child) {
  margin-bottom: 30px;
}
@media (max-width: 575px) {
  .shop-main-sidebar .single-sidebar-widget {
    margin-bottom: 25px;
  }
}
.shop-main-sidebar .single-sidebar-widget .wid-title {
  margin-bottom: 20px;
  padding-left: 10px;
  position: relative;
}
.shop-main-sidebar .single-sidebar-widget .wid-title::before {
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  content: "";
  width: 2px;
  height: 21px;
  position: absolute;
  background-color: var(--theme);
}
.shop-main-sidebar .single-sidebar-widget .wid-title h4 {
  font-size: 20px;
  font-weight: 700;
}
.shop-main-sidebar .single-sidebar-widget .shop-catagory-items ul li {
  transition: all 0.4s ease-in-out;
}
.shop-main-sidebar .single-sidebar-widget .shop-catagory-items ul li:not(:last-child) {
  margin-bottom: 20px;
}
.shop-main-sidebar .single-sidebar-widget .shop-catagory-items ul li a {
  font-weight: 500;
  color: var(--header);
  text-transform: capitalize;
  position: relative;
  padding-left: 20px;
}
.shop-main-sidebar .single-sidebar-widget .shop-catagory-items ul li a::before {
  position: absolute;
  top: -3px;
  left: 0;
  content: "\f054";
  font-family: "Font Awesome 5 Pro";
}
.shop-main-sidebar .single-sidebar-widget .shop-catagory-items ul li a i {
  margin-right: 5px;
  color: #615F5D;
}
.shop-main-sidebar .single-sidebar-widget .shop-catagory-items ul li:hover {
  margin-left: 5px;
}
.shop-main-sidebar .single-sidebar-widget .shop-catagory-items ul li:hover a {
  color: var(--theme);
}
.shop-main-sidebar .single-sidebar-widget .shop-catagory-items ul li:hover a i {
  color: var(--theme);
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom .price-input {
  margin-top: 30px;
  position: relative;
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom .price-input .field {
  display: flex;
  align-items: center;
  font-size: 16px;
  width: 24%;
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom .price-input .field span {
  font-size: 16px;
  font-weight: 500;
  color: var(--header);
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom .separators {
  margin-left: -25px;
  padding-right: 20px;
  font-size: 24px;
  line-height: 42px;
  font-weight: 500;
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom .field input {
  height: 100%;
  outline: none;
  background: transparent;
  border: unset;
  font-size: 16px;
  font-weight: 500;
  color: var(--header);
  padding: 0;
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom .field input span {
  font-weight: 500;
  color: var(--header);
  font-size: 16px;
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom input[type=number]::-webkit-outer-spin-button,
.shop-main-sidebar .single-sidebar-widget .range__barcustom input[type=number]::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom .price-input .separator {
  font-size: 14px;
  font-weight: 400;
  color: var(--white);
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom .slider {
  height: 6.75px;
  position: relative;
  background: #E3E5F1;
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom .slider .progress {
  height: 100%;
  left: 25%;
  right: 25%;
  position: absolute;
  border-radius: 5px;
  background: var(--theme);
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom .range-input {
  position: relative;
  display: flex;
  justify-content: center;
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom .range-input input {
  position: absolute;
  width: 100%;
  height: 6.75px;
  top: -7px;
  background: none;
  pointer-events: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  padding: 0;
  outline: none;
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom input[type=range]::-webkit-slider-thumb {
  height: 17px;
  width: 7px;
  border-radius: 5px;
  background: var(--theme);
  border: 1.125px solid var(--theme);
  pointer-events: auto;
  -webkit-appearance: none;
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom .price-input .theme-btn {
  padding: 14px 30px;
  border-radius: 0;
  min-width: initial;
  background-color: var(--theme);
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom .price-input .theme-btn::before {
  background-color: var(--header);
}
.shop-main-sidebar .single-sidebar-widget .filter-size .input-save:not(:last-child) {
  margin-bottom: 20px;
}
.shop-main-sidebar .single-sidebar-widget .filter-size .input-save input {
  width: 20px;
  height: 19px;
  background-color: var(--theme);
  outline: none;
  color: var(--theme2);
  padding: 5px;
  border-radius: 4px;
}
.shop-main-sidebar .single-sidebar-widget .filter-size .input-save label {
  margin-left: 20px;
  color: var(--header);
  text-transform: capitalize;
  font-weight: 600;
  text-transform: capitalize;
}
.shop-main-sidebar .single-sidebar-widget .filter-size .checkbox-single {
  position: relative;
  padding-left: 18px;
  cursor: pointer;
  display: inherit;
}
.shop-main-sidebar .single-sidebar-widget .filter-size .checkbox-single input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}
.shop-main-sidebar .single-sidebar-widget .filter-size .checkbox-single:not(:last-child) {
  margin-bottom: 20px;
}
.shop-main-sidebar .single-sidebar-widget .filter-size .checkbox-single .checkbox-area .checkmark {
  position: absolute;
  left: 0;
  height: 20px;
  width: 20px;
  border: 1px solid #E7EAF3;
  top: 5px;
  background-color: var(--white);
  border-radius: 3px;
}
.shop-main-sidebar .single-sidebar-widget .filter-size .checkbox-single .checkbox-area .checkmark::after {
  content: "";
}
.shop-main-sidebar .single-sidebar-widget .filter-size .checkbox-single .checkbox-area input:checked ~ .checkmark {
  background-color: var(--theme);
}
.shop-main-sidebar .single-sidebar-widget .filter-size .checkbox-single .checkbox-area input:checked ~ .checkmark::after {
  content: "\f00c";
  position: absolute;
  font-family: "Font Awesome 5 Pro";
  top: -5px;
  left: 5px;
  color: var(--theme);
  font-size: 11px;
}
.shop-main-sidebar .single-sidebar-widget .filter-size .checkbox-single .checkbox-area input:checked ~ .checkmark:after {
  display: block;
  color: var(--white);
}
.shop-main-sidebar .single-sidebar-widget .filter-size .checkbox-single .text-color {
  font-weight: 500;
  color: var(--header);
  font-size: 16px;
}
.shop-main-sidebar .single-sidebar-widget .filter-size .checkbox-single .text-color .star {
  color: #FFA800;
  margin-right: 5px;
}
.shop-main-sidebar .single-sidebar-widget .filter-size .checkbox-single .text-color .star i.color-2 {
  color: #E1E2E7;
}
.shop-main-sidebar .single-sidebar-widget .color-list {
  display: flex;
  align-items: center;
  gap: 15px;
}
.shop-main-sidebar .single-sidebar-widget .color-list li {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background-color: #C4C4C4;
  cursor: pointer;
}
.shop-main-sidebar .single-sidebar-widget .color-list li:nth-child(2) {
  background-color: #ED0707;
}
.shop-main-sidebar .single-sidebar-widget .color-list li:nth-child(3) {
  background-color: #00A651;
}
.shop-main-sidebar .single-sidebar-widget .color-list li:nth-child(4) {
  background-color: #FEE496;
}
.shop-main-sidebar .single-sidebar-widget .color-list li:nth-child(5) {
  background-color: #2588BF;
}
.shop-main-sidebar .single-sidebar-widget .color-list li:nth-child(6) {
  background-color: #000000;
}
.shop-main-sidebar .single-sidebar-widget .shop-widget-tag span {
  color: #77787B;
  font-size: 16px;
  font-weight: 500;
  text-transform: capitalize;
  display: inline-block;
  padding: 0 15px;
  height: 45px;
  line-height: 42px;
  background-color: transparent;
  margin-bottom: 15px;
  margin-right: 3px;
  transition: 0.3s;
  border: 1px solid #EFF2F6;
}
.shop-main-sidebar .single-sidebar-widget .shop-widget-tag span:hover {
  background-color: var(--theme);
  color: var(--white);
}

.woocommerce-notices-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #EDEEF3;
  padding: 0 20px;
  position: relative;
  z-index: 9;
}
@media (max-width: 767px) {
  .woocommerce-notices-wrapper {
    flex-wrap: wrap;
    padding: 20px 15px;
    gap: 20px;
    justify-content: center;
  }
}
.woocommerce-notices-wrapper p {
  font-weight: 600;
  font-size: 16px;
  color: var(--header);
}
.woocommerce-notices-wrapper p span {
  color: var(--theme);
}
.woocommerce-notices-wrapper .form-clt {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  z-index: 99;
}
.woocommerce-notices-wrapper .form-clt .nice-select {
  padding: 18px 20px;
  font-size: 16px;
  text-transform: capitalize;
  border-radius: 0;
  background-color: transparent;
  font-weight: 500;
  border: none;
}
@media (max-width: 767px) {
  .woocommerce-notices-wrapper .form-clt .nice-select {
    padding: 0 20px;
  }
}
.woocommerce-notices-wrapper .form-clt .nice-select::after {
  border-bottom: 1px solid var(--text);
  border-right: 1px solid var(--text);
  height: 10px;
  width: 10px;
  right: -5px;
  top: 30px;
}
@media (max-width: 767px) {
  .woocommerce-notices-wrapper .form-clt .nice-select::after {
    top: 15px;
  }
}
.woocommerce-notices-wrapper .form-clt .nice-select .list {
  right: -80px;
  background-color: var(--bg);
  width: 220px;
  padding: 12px 20px;
  border-radius: 0;
}
.woocommerce-notices-wrapper .form-clt .nice-select .option {
  border: none;
  font-size: 14px;
}
.woocommerce-notices-wrapper .form-clt .nice-select span {
  color: var(--text);
}
.woocommerce-notices-wrapper .form-clt .icon {
  margin-left: 10px;
}
.woocommerce-notices-wrapper .form-clt .icon a {
  color: var(--header);
}
.woocommerce-notices-wrapper .form-clt .icon-2 a {
  color: var(--theme);
}

.main-cart-wrapper {
  border-radius: 5px;
}
.main-cart-wrapper .cart-wrapper {
  box-shadow: 0px 6px 30px rgba(0, 0, 0, 0.06);
  padding: 40px 40px;
}
.main-cart-wrapper .cart-wrapper .cart-items-wrapper table {
  width: 100%;
}
.main-cart-wrapper .cart-wrapper .cart-items-wrapper table thead {
  border-bottom: 1px solid var(--border);
}
.main-cart-wrapper .cart-wrapper .cart-items-wrapper table thead tr th {
  padding-bottom: 16px;
  color: var(--header);
  text-transform: capitalize;
}
.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item td {
  border-bottom: 1px solid var(--border);
  padding: 16px 0;
}
.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item .cart-item-info {
  display: flex;
  align-items: center;
  gap: 16px;
}
.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item .cart-item-price {
  color: var(--header);
}
.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item .cart-item-price-2 {
  color: var(--header);
}
.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item .cart-item-price-2 .total-prices {
  color: var(--header);
}
.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item .cart-item-quantity {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background: var(--bg);
  width: 100px;
  border-radius: 5px;
  padding: 2px 20px;
}
.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item .cart-item-quantity span {
  display: block;
  color: var(--header);
}
.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item .cart-item-quantity .cart-item-quantity-controller a {
  display: block;
}
.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item .cart-item-quantity .cart-item-quantity-controller a i {
  color: var(--header);
}
.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item .quantity-basket .qty {
  display: inline-flex;
  align-items: center;
  border: 1px solid var(--border);
  padding: 10px 20px;
  line-height: 1;
  justify-content: space-between;
}
.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item .quantity-basket .qty button,
.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item .quantity-basket .qty input {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text);
  font-weight: 400;
  font-size: 22px;
}
.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item .quantity-basket .qty input {
  text-align: center;
  border-radius: 0;
  border: unset;
  outline: none;
}
.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item .cart-item-remove a i {
  color: var(--header);
}
.main-cart-wrapper .cart-wrapper-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}
.main-cart-wrapper .cart-wrapper-footer form {
  border: 1px solid var(--border);
  padding: 8px;
  border-radius: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.main-cart-wrapper .cart-wrapper-footer form input {
  padding: 5px 5px;
  border: none;
  text-transform: capitalize;
  font-size: 16px;
  outline: none;
  background: transparent;
  color: var(--text);
}
.main-cart-wrapper .cart-wrapper-footer form button {
  outline: none;
  border: none;
}
.main-cart-wrapper .cart-pragh-box {
  margin-top: 24px;
  padding-right: 30px;
}
.main-cart-wrapper .cart-graph {
  border: 1px solid var(--border);
  padding: 30px 30px;
  border-radius: 5px;
}
.main-cart-wrapper .cart-graph h4 {
  text-align: center;
  color: var(--header);
  margin-bottom: 30px;
}
.main-cart-wrapper .cart-graph ul {
  margin-bottom: 30px;
}
.main-cart-wrapper .cart-graph ul li {
  display: flex;
}
.main-cart-wrapper .cart-graph ul li:not(:last-child) {
  border-bottom: 1px solid var(--border);
}
.main-cart-wrapper .cart-graph ul li span {
  width: 50%;
  color: var(--header);
  font-size: 18px;
  text-transform: capitalize;
  padding: 16px 0;
  font-weight: 500;
}

@media (max-width: 767px) {
  .cart-wrapper {
    overflow-x: scroll;
  }
  .cart-wrapper .cart-items-wrapper {
    width: 700px;
  }
  .cart-wrapper .cart-wrapper-footer {
    width: 700px;
  }
}
.checkout-radio {
  box-shadow: 0px 6px 30px rgba(0, 0, 0, 0.06);
  border-radius: 5px;
  padding: 24px;
}
.checkout-radio .primary-text {
  font-size: 24px;
  font-weight: 500;
  line-height: 150%;
  margin-bottom: 16px;
  color: var(--header);
  text-transform: capitalize;
}
.checkout-radio h4 {
  color: var(--header);
  margin-bottom: 16px;
  font-weight: 600;
}
.checkout-radio .checkout-radio-wrapper .checkout-radio-single .form-check-input {
  border-radius: 50%;
  width: 18px;
  height: 18px;
  box-shadow: none;
  outline: none;
  border: 1px solid var(--border);
  font-weight: 500;
}
.checkout-radio .checkout-radio-wrapper .checkout-radio-single label {
  color: var(--header);
  text-transform: capitalize;
}
.checkout-radio .checkout-radio-wrapper .checkout-radio-single:not(:last-child) {
  margin-bottom: 12px;
}
@media (max-width: 500px) {
  .checkout-radio {
    padding: 10px;
  }
}

.checkout-single-wrapper .checkout-single h4 {
  color: var(--header);
  margin-bottom: 2rem;
  font-weight: 600;
}
.checkout-single-wrapper .checkout-single .checkout-single-form .input-single textarea,
.checkout-single-wrapper .checkout-single .checkout-single-form .input-single .country-select,
.checkout-single-wrapper .checkout-single .checkout-single-form .input-single input {
  width: 100%;
  outline: none;
  box-shadow: none;
  border: 1px solid var(--border);
  border-radius: 5px;
  padding: 12px 24px;
  color: var(--header);
  text-transform: capitalize;
  font-weight: 500;
}
.checkout-single-wrapper .checkout-single .checkout-single-form .input-single label {
  color: var(--header);
  font-size: 18px;
  text-transform: capitalize;
  margin-bottom: 10px;
  font-weight: 500;
}
.checkout-single-wrapper .checkout-single .checkout-single-form .input-single ::placeholder {
  color: var(--header);
}
.checkout-single-wrapper .checkout-single .checkout-single-form .input-single .nice-select {
  background-color: var(--white);
}
.checkout-single-wrapper .checkout-single .checkout-single-form .input-single .nice-select span {
  font-size: 18px;
  color: var(--header);
  font-weight: 500;
}
.checkout-single-wrapper .checkout-single .checkout-single-form .input-single .nice-select::after {
  border-right: 1px solid var(--header);
  border-bottom: 1px solid var(--header);
}
.checkout-single-wrapper .checkout-single .checkout-single-form .payment {
  color: var(--header);
  margin-bottom: 12px;
  text-transform: capitalize;
}
.checkout-single-wrapper .boxshado-single {
  box-shadow: 0px 6px 30px rgba(0, 0, 0, 0.06);
  border-radius: 5px;
  padding: 32px;
  margin-bottom: 24px;
}
@media (max-width: 575px) {
  .checkout-single-wrapper .boxshado-single {
    padding: 14px;
  }
}
.checkout-single-wrapper .checkout-single-bg {
  box-shadow: 0px 6px 30px rgba(0, 0, 0, 0.06);
  border-radius: 5px;
  padding: 32px;
}
.checkout-single-wrapper .checkout-single-bg .checkout-single-form .input-single textarea,
.checkout-single-wrapper .checkout-single-bg .checkout-single-form .input-single .country-select,
.checkout-single-wrapper .checkout-single-bg .checkout-single-form .input-single input {
  border: 1px solid var(--border);
  background: transparent;
  text-transform: capitalize;
}
@media (max-width: 575px) {
  .checkout-single-wrapper .checkout-single-bg {
    padding: 14px;
  }
}
.checkout-single-wrapper .checkout-single-bg .payment-save {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
}
.checkout-single-wrapper .checkout-single-bg .payment-save input {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px solid var(--border);
  background-color: var(--theme);
  outline: none;
  color: var(--header);
}
.checkout-single-wrapper .checkout-single-bg .payment-save label {
  font-size: 16px;
  font-weight: 500;
  text-transform: capitalize;
}

.shop-details-wrapper .shop-details-image {
  position: relative;
}
.shop-details-wrapper .shop-details-image .nav {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-top: 20px;
}
.shop-details-wrapper .shop-details-image .nav img {
  width: 100%;
  height: 100%;
  border-radius: 5px;
}
.shop-details-wrapper .shop-details-image .nav .nav-link {
  padding: 0;
  max-width: 115px;
}
.shop-details-wrapper .shop-details-image .shop-thumb img {
  width: 100%;
  height: 100%;
  border-radius: 5px;
}
.shop-details-wrapper .product-details-content {
  margin-left: 60px;
}
@media (max-width: 1399px) {
  .shop-details-wrapper .product-details-content {
    margin-left: 30px;
  }
}
@media (max-width: 991px) {
  .shop-details-wrapper .product-details-content {
    margin-left: 0;
  }
}
.shop-details-wrapper .product-details-content .star a {
  color: var(--theme);
  font-size: 16px;
  font-weight: 600;
}
.shop-details-wrapper .product-details-content .star span {
  margin-left: 10px;
}
.shop-details-wrapper .product-details-content .price-list {
  gap: 20px;
  border-bottom: 1px solid var(--border);
  padding-bottom: 30px;
}
.shop-details-wrapper .product-details-content .price-list h3 {
  font-size: 40px;
}
.shop-details-wrapper .product-details-content .cart-wrp {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
  margin-top: 30px;
  margin-bottom: 30px;
}
.shop-details-wrapper .product-details-content .cart-wrp .cart-quantity {
  padding-right: 15px;
  margin-right: 15px;
}
.shop-details-wrapper .product-details-content .cart-wrp .cart-quantity input {
  width: 50px;
  height: 40px;
  text-align: center;
  background-color: transparent;
  border: 1px solid var(--border);
  color: var(--header);
  font-weight: 700;
}
.shop-details-wrapper .product-details-content .cart-wrp .cart-quantity input:hover {
  background: var(--theme);
  color: var(--white);
  border-color: var(--theme);
}
.shop-details-wrapper .product-details-content .cart-wrp .cart-quantity .minus {
  color: var(--header);
}
.shop-details-wrapper .product-details-content .cart-wrp .cart-quantity .plus {
  color: var(--header);
}
.shop-details-wrapper .product-details-content .cart-wrp .cart-quantity .qty {
  color: var(--header);
}
.shop-details-wrapper .product-details-content .cart-wrp .icon {
  width: 50px;
  height: 40px;
  text-align: center;
  background-color: transparent;
  border: 1px solid var(--border);
  color: var(--header);
  line-height: 40px;
}
.shop-details-wrapper .product-details-content .cart-wrp .icon:hover {
  background: var(--theme);
  color: var(--white);
  border-color: var(--theme);
}
.shop-details-wrapper .product-details-content .cart-wrp .theme-btn {
  padding: 20px 35px;
}
.shop-details-wrapper .product-details-content .shop-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 30px;
}
.shop-details-wrapper .product-details-content .shop-btn .theme-btn {
  padding: 20px 50px;
}
.shop-details-wrapper .product-details-content .shop-btn .theme-btn:nth-child(2) {
  background-color: var(--header);
}
.shop-details-wrapper .product-details-content .shop-btn .theme-btn:nth-child(2)::before, .shop-details-wrapper .product-details-content .shop-btn .theme-btn:nth-child(2)::after {
  background-color: var(--theme);
}
.shop-details-wrapper .product-details-content .details-info {
  position: relative;
  margin-top: 20px;
}
.shop-details-wrapper .product-details-content .details-info span {
  font-size: 16px;
  font-weight: 600;
  text-transform: capitalize;
  margin-right: 5px;
}
.shop-details-wrapper .product-details-content .details-info a {
  font-size: 16px;
  font-weight: 400;
  color: var(--text);
  text-transform: capitalize;
}
.shop-details-wrapper .product-details-content .details-info.style-2 a b {
  border: 1px solid #e6e6e6;
  padding: 4px 12px 6px;
  font-weight: 400;
  font-size: 14px;
  transition: all 0.4s ease-in-out;
}
.shop-details-wrapper .product-details-content .details-info.style-2 a b:hover {
  background-color: var(--theme);
  color: var(--white);
}
.shop-details-wrapper .single-tab {
  padding-top: 60px;
}
.shop-details-wrapper .single-tab .nav {
  justify-content: center;
  border-bottom: 1px solid var(--border);
  padding-bottom: 20px;
}
.shop-details-wrapper .single-tab .nav .nav-link.active {
  position: relative;
}
.shop-details-wrapper .single-tab .nav .nav-link.active h6 {
  color: var(--theme);
}
.shop-details-wrapper .single-tab .nav .nav-link.active::before {
  position: absolute;
  bottom: -21px;
  left: 0;
  height: 2px;
  width: 100%;
  content: "";
  background: var(--theme);
  transition: 0.3s;
}
@media (max-width: 470px) {
  .shop-details-wrapper .single-tab .nav .nav-link.active::before {
    display: none;
  }
}
.shop-details-wrapper .single-tab .nav .nav-link h6 {
  font-size: 18px;
}
.shop-details-wrapper .single-tab .description-items .description-content {
  margin-right: 50px;
}
@media (max-width: 1399px) {
  .shop-details-wrapper .single-tab .description-items .description-content {
    margin-right: 30px;
  }
}
@media (max-width: 991px) {
  .shop-details-wrapper .single-tab .description-items .description-content {
    margin-right: 0;
  }
}
.shop-details-wrapper .single-tab .description-items .description-content h3 {
  margin-bottom: 15px;
}
.shop-details-wrapper .single-tab .description-items .description-content .description-list-items {
  margin-top: 30px;
}
@media (max-width: 575px) {
  .shop-details-wrapper .single-tab .description-items .description-content .description-list-items {
    flex-wrap: wrap;
    gap: 30px;
  }
}
.shop-details-wrapper .single-tab .description-items .description-content .description-list-items .description-list li {
  font-size: 16px;
  text-transform: capitalize;
  color: var(--header);
  font-weight: 600;
}
.shop-details-wrapper .single-tab .description-items .description-content .description-list-items .description-list li span {
  color: var(--text);
  font-weight: 400;
}
@media (max-width: 767px) {
  .shop-details-wrapper .single-tab .review-items .admin-items {
    justify-content: center;
    gap: 30px;
  }
}
.shop-details-wrapper .single-tab .review-items .admin-items .admin-img {
  width: 100px;
  height: 100px;
}
.shop-details-wrapper .single-tab .review-items .admin-items .admin-img img {
  border-radius: 50%;
}
.shop-details-wrapper .single-tab .review-items .admin-items .content {
  position: relative;
  border: 1px solid var(--border);
}
.shop-details-wrapper .single-tab .review-items .admin-items .content::before {
  position: absolute;
  content: "";
  width: 30px;
  height: 30px;
  background-color: var(--white);
  border-left: 1px solid var(--border);
  border-bottom: 1px solid var(--border);
  transform: rotate(45deg);
  top: 40%;
  left: -16px;
}
@media (max-width: 575px) {
  .shop-details-wrapper .single-tab .review-items .admin-items .content::before {
    display: none;
  }
}
.shop-details-wrapper .single-tab .review-items .admin-items .content .head-content h5 span {
  font-size: 14px;
  margin-left: 30px;
}
.shop-details-wrapper .single-tab .review-items .admin-items .content .star i {
  font-size: 16px;
  color: var(--theme);
}
.shop-details-wrapper .single-tab .review-title .rate-now {
  margin-top: 15px;
  gap: 15px;
}
.shop-details-wrapper .single-tab .review-title .rate-now p {
  font-size: 16px;
  text-transform: capitalize;
}
.shop-details-wrapper .single-tab .review-title .rate-now i {
  font-size: 16px;
  color: var(--theme);
}
.shop-details-wrapper .single-tab .review-form {
  margin-top: 40px;
}
.shop-details-wrapper .single-tab .review-form .form-clt input {
  width: 100%;
  outline: none;
  border: none;
  padding: 18px 35px;
  color: var(--text);
  background-color: rgb(245, 245, 245);
  font-size: 16px;
  text-transform: capitalize;
}
@media (max-width: 575px) {
  .shop-details-wrapper .single-tab .review-form .form-clt input {
    padding: 15px 20px;
  }
}
.shop-details-wrapper .single-tab .review-form .form-clt-big textarea {
  padding: 18px 35px 170px;
  width: 100%;
  outline: none;
  color: var(--text);
  background-color: rgb(245, 245, 245);
  font-size: 16px;
  border: none;
  text-transform: capitalize;
}
@media (max-width: 575px) {
  .shop-details-wrapper .single-tab .review-form .form-clt-big textarea {
    padding: 15px 20px;
  }
}

.signin-item {
  background-color: var(--bg);
  padding: 60px;
}
.signin-item h3 {
  font-size: 42px;
  line-height: 64px;
  font-weight: 300;
  text-align: center;
  padding-bottom: 30px;
  border-bottom: 1px solid rgba(6, 18, 14, 0.1215686275);
  margin-bottom: 50px;
}
@media (max-width: 767px) {
  .signin-item h3 {
    font-size: 36px;
    line-height: 50px;
  }
}
.signin-item label {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  color: var(--header);
  display: block;
}
.signin-item input {
  width: 100%;
  padding: 16px 25px;
  border: none;
  margin-bottom: 24px;
  color: var(--text);
}
.signin-item .info {
  margin-top: 20px;
}
.signin-item .info a {
  font-weight: 500;
  color: #06120e;
}
.signin-item .info p {
  font-weight: 500;
}
.signin-item .info .line1 {
  margin: 20px 0;
}
.signin-item .form-check input {
  width: unset;
  padding: 10px;
  border: 1px solid #06120e;
  cursor: pointer;
  box-shadow: none;
}
.signin-item .theme-btn.google-btn {
  background-color: transparent;
  color: var(--header);
  border: 1px solid var(--theme);
  padding: 18.5px 45px;
}
.signin-item .theme-btn.google-btn::before, .signin-item .theme-btn.google-btn::after {
  background-color: var(--header);
}
.signin-item .theme-btn.google-btn:hover {
  color: var(--white);
}

.catagories-icon-items {
  margin-top: 30px;
  background-color: var(--white);
  text-align: center;
  padding: 50px 30px;
}
.catagories-icon-items .icon {
  width: 72px;
  height: 72px;
  line-height: 72px;
  border-radius: 50%;
  background-color: #F3F6FF;
  margin: 0 auto;
}
.catagories-icon-items .content {
  margin-top: 20px;
}
.catagories-icon-items .content p {
  margin-bottom: 5px;
}
.catagories-icon-items .content h3 a:hover {
  color: var(--theme);
}
.catagories-icon-items .content .arrow-icon {
  background: linear-gradient(90deg, #FF4E8D 0%, #7807FB 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
  margin-top: 15px;
}
.catagories-icon-items .content .arrow-icon i {
  margin-left: 7px;
}

.service-wrapper {
  margin-right: -25%;
}
@media (max-width: 767px) {
  .service-wrapper {
    margin-right: 0;
  }
}
.service-wrapper .service-image {
  margin-top: 30px;
  position: relative;
}
.service-wrapper .service-image img {
  width: 100%;
  height: 100%;
  border-radius: 15px;
}
.service-wrapper .service-image::before {
  background-image: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 19.36%, rgba(112, 0, 254, 0.55) 71.26%, #7000fe 100%);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  content: "";
  overflow: hidden;
  opacity: 0;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  visibility: hidden;
  content: "";
  border-radius: 0 0 15px 15px;
}
.service-wrapper .service-image .icon {
  position: absolute;
  top: 0;
  left: 50%;
  width: 90px;
  height: 90px;
  border-radius: 50%;
  line-height: 95px;
  background-color: var(--white);
  text-align: center;
  color: var(--theme);
  font-size: 20px;
  transform: translate(-50%, -50%) rotate(-40deg);
  transition: all 0.4s ease-in-out;
  opacity: 0;
  visibility: hidden;
}
@media (max-width: 575px) {
  .service-wrapper .service-image .icon {
    width: 60px;
    height: 60px;
    line-height: 65px;
  }
}
.service-wrapper .service-image .icon:hover {
  background-color: var(--theme);
  color: var(--white);
  transform: translate(-50%, -50%) rotate(0);
}
.service-wrapper .service-image .service-content {
  position: absolute;
  bottom: 30px;
  left: 30px;
  background-color: var(--white);
  padding: 15px 30px;
  border-radius: 20px;
  transition: all 0.4s ease-in-out;
}
.service-wrapper .service-image .service-content h3 a:hover {
  color: var(--theme);
}
.service-wrapper .service-image:hover::before {
  opacity: 1;
  visibility: visible;
}
.service-wrapper .service-image:hover .service-content {
  background-color: transparent;
}
.service-wrapper .service-image:hover .service-content h3 a {
  color: var(--white);
}
.service-wrapper .service-image:hover .icon {
  opacity: 1;
  visibility: visible;
  top: 50%;
}

.service-wrapper-2 .service-box-items {
  background-color: var(--white);
  box-shadow: var(---box-shadow);
  padding: 40px 25px;
}
.service-wrapper-2 .service-box-items:not(:last-child) {
  margin-bottom: 30px;
}
.service-wrapper-2 .service-box-items .icon {
  width: 110px;
  height: 110px;
  line-height: 115px;
  text-align: center;
  border-radius: 50%;
  font-size: 52px;
  background: var(--color-gradient-1);
  text-align: center;
  color: var(--white);
}
.service-wrapper-2 .service-box-items .content {
  margin-top: 25px;
}
.service-wrapper-2 .service-box-items .content h3 {
  margin-bottom: 10px;
}
.service-wrapper-2 .service-box-items .content h3 a:hover {
  color: var(--theme);
}
.service-wrapper-2 .service-box-items.style-2 {
  margin-top: 50px;
}
.service-wrapper-2 .service-right-content {
  margin-left: 40px;
}
@media (max-width: 1199px) {
  .service-wrapper-2 .service-right-content {
    margin-left: 0;
  }
}
.service-wrapper-2 .service-right-content p {
  margin-bottom: 30px;
}
.service-wrapper-2 .service-right-content .icon-items {
  display: flex;
  align-items: center;
  gap: 15px;
}
.service-wrapper-2 .service-right-content .icon-items:not(:last-child) {
  margin-bottom: 30px;
}
.service-wrapper-2 .service-right-content .icon-items .icon {
  width: 70px;
  height: 70px;
  line-height: 70px;
  text-align: center;
  border-radius: 50%;
  background-color: #f4f4f4;
}
.service-wrapper-2 .service-right-content .theme-btn {
  margin-top: 40px;
}
@media (max-width: 767px) {
  .service-wrapper-2 .service-right-content .theme-btn {
    margin-top: 30px;
  }
}
@media (max-width: 575px) {
  .service-wrapper-2 .service-right-content .theme-btn {
    margin-top: 25px;
  }
}

.service-section {
  position: relative;
}
.service-section .bg-shape {
  position: absolute;
  top: 0;
  left: -20%;
  z-index: -1;
}

.service-card-items {
  margin-top: 30px;
  display: flex;
  align-items: center;
  gap: 20px;
  background-color: var(--white);
  box-shadow: var(---box-shadow);
  padding: 50px;
}
.service-card-items .service-content h3 {
  margin-bottom: 10px;
}
.service-card-items .service-content h3 a:hover {
  color: var(--theme);
}
.service-card-items .service-content p {
  margin-bottom: 15px;
}
.service-card-items .service-content .link-btn:hover {
  color: var(--theme);
}
@media (max-width: 767px) {
  .service-card-items {
    padding: 30px;
    flex-wrap: wrap;
    justify-content: center;
    text-align: center;
  }
}

.service-details-wrapper .service-details-image img {
  width: 100%;
  height: 100%;
}
.service-details-wrapper .service-details-content {
  margin-top: 30px;
}
.service-details-wrapper .service-details-content h3 {
  font-size: 36px;
}
.service-details-wrapper .service-details-content p {
  font-weight: 400;
}
.service-details-wrapper .service-details-content .service-details-video {
  margin-top: 50px;
  margin-bottom: 40px;
}
@media (max-width: 575px) {
  .service-details-wrapper .service-details-content .service-details-video {
    margin-top: 25px;
    margin-bottom: 30px;
  }
}
@media (max-width: 575px) {
  .service-details-wrapper .service-details-content .service-details-video {
    margin-bottom: 20px;
    margin-top: 20px;
  }
}
.service-details-wrapper .service-details-content .service-details-video .details-video-content ul li {
  font-weight: 500;
  font-size: 18px;
  color: var(--header);
}
.service-details-wrapper .service-details-content .service-details-video .details-video-content ul li:not(:last-child) {
  margin-bottom: 15px;
}
.service-details-wrapper .service-details-content .service-details-video .details-video-content ul li i {
  color: var(--theme);
  margin-right: 8px;
}
.service-details-wrapper .service-details-content .service-details-video .video-image {
  position: relative;
}
.service-details-wrapper .service-details-content .service-details-video .video-image img {
  width: 100%;
  height: 100%;
}
.service-details-wrapper .service-details-content .service-details-video .video-image .video-box {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.service-details-wrapper .service-details-content .service-details-video .video-image .video-box .video-btn {
  background-color: var(--white);
  width: 80px;
  line-height: 80px;
  height: 80px;
  text-align: center;
  color: var(--theme);
  border-radius: 50%;
  display: inline-block;
}
.service-details-wrapper .service-details-content .service-details-video .video-image .video-box .ripple::before, .service-details-wrapper .service-details-content .service-details-video .video-image .video-box .ripple::after {
  width: 80px;
  height: 80px;
  box-shadow: 0 0 0 0 rgba(8, 189, 201, 0.6);
}
.service-details-wrapper .service-details-content .highlight-text {
  margin-top: 30px;
  padding: 18px;
  border-left: 4px solid var(--theme);
  box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.06);
  background: var(--white);
}
.service-details-wrapper .service-details-content .highlight-text h5 {
  font-weight: 600;
  line-height: 167%;
}
.service-details-wrapper .service-details-content .service-image-item {
  margin-top: 60px;
  margin-bottom: 60px;
}
.service-details-wrapper .service-details-content .service-image-item h3 {
  font-size: 24px;
}
.service-details-wrapper .service-details-content .service-image-item .service-box-image {
  height: 218px;
}
.service-details-wrapper .service-details-content .service-image-item .service-box-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.service-sidebar .service-widget-categories {
  margin-bottom: 30px;
  background-color: var(--bg);
  padding: 30px;
}
.service-sidebar .service-widget-categories h4 {
  margin-bottom: 30px;
  font-size: 22px;
}
.service-sidebar .service-widget-categories ul li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 22px 25px;
  background-color: var(--white);
  font-weight: 500;
  transition: all 0.4s ease-in-out;
  border-radius: 0;
  font-size: 18px;
  line-height: 1;
  font-weight: 500;
  font-family: "Outfit", sans-serif;
}
.service-sidebar .service-widget-categories ul li a {
  color: var(--header);
}
.service-sidebar .service-widget-categories ul li span {
  transition: all 0.4s ease-in-out;
  color: var(--header);
}
.service-sidebar .service-widget-categories ul li:not(:last-child) {
  margin-bottom: 12px;
}
.service-sidebar .service-widget-categories ul li:hover {
  background-color: var(--theme);
}
.service-sidebar .service-widget-categories ul li:hover a {
  color: var(--white);
}
.service-sidebar .service-widget-categories ul li:hover span {
  color: var(--white);
}
.service-sidebar .service-widget-categories ul li.active {
  background-color: var(--theme);
}
.service-sidebar .service-widget-categories ul li.active a {
  color: var(--white);
}
.service-sidebar .service-widget-categories ul li.active span {
  color: var(--white);
}
.service-sidebar .contact-bg {
  padding: 40px 25px;
  text-align: center;
}
.service-sidebar .contact-bg .icon {
  width: 86.526px;
  height: 76px;
  text-align: center;
  line-height: 76px;
  border-radius: 4px;
  background-color: var(--theme);
  font-size: 32px;
  color: var(--white);
  margin: 0 auto 25px;
}
.service-sidebar .contact-bg h3 {
  font-size: 26px;
  color: var(--white);
  line-height: 139%;
  margin-bottom: 30px;
}
.service-sidebar .contact-bg p {
  color: var(--white);
  font-size: 15px;
  font-weight: 400;
  letter-spacing: 1.95px;
  text-transform: uppercase;
  margin-bottom: 15px;
}
.service-sidebar .contact-bg .theme-btn {
  padding: 18.5px 50px;
  background-color: var(--theme);
}
.service-sidebar .contact-bg .theme-btn::before {
  background-color: var(--header);
}

.project-image-items {
  margin-top: 30px;
  position: relative;
  overflow: hidden;
}
.project-image-items img {
  width: 100%;
  height: 100%;
}
.project-image-items br {
  display: block !important;
}
.project-image-items::before {
  position: absolute;
  content: "";
  left: 0;
  bottom: -500px;
  width: 100%;
  height: 100%;
  background-image: -moz-linear-gradient(90deg, rgb(112, 0, 254) 0%, rgba(112, 0, 254, 0) 100%);
  background-image: -webkit-linear-gradient(90deg, rgb(112, 0, 254) 0%, rgba(112, 0, 254, 0) 100%);
  background-image: -ms-linear-gradient(90deg, rgb(112, 0, 254) 0%, rgba(112, 0, 254, 0) 100%);
  -webkit-transition: 0.5s;
  transition: 0.5s;
  visibility: hidden;
  opacity: 0;
}
.project-image-items .project-content {
  position: absolute;
  bottom: -100px;
  left: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  right: 40px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease-in-out;
}
.project-image-items .project-content .content h6 {
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--white);
}
.project-image-items .project-content .content h3 a {
  color: var(--white);
}
.project-image-items .project-content .icon-btn {
  display: flex;
  align-items: center;
  gap: 10px;
}
.project-image-items .project-content .icon-btn .icon {
  width: 40px;
  height: 40px;
  color: var(--white);
  background-color: transparent;
  text-align: center;
  display: inline-block;
  line-height: 40px;
  border: 1px solid var(--white);
  transition: all 0.4s ease-in-out;
}
.project-image-items .project-content .icon-btn .icon:hover {
  background-color: var(--white);
  color: var(--header);
}
.project-image-items .hover-button {
  position: absolute;
  top: 50%;
  inset-inline-start: 0;
  transform: translateY(-50%) rotate(10deg);
  opacity: 0;
  transition: opacity 0.3s, transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
  margin: -250px 0 0 -50px;
  overflow: hidden;
  z-index: 9;
  visibility: hidden;
  width: 110px;
  height: 110px;
  text-align: center;
  border-radius: 50%;
  background-color: var(--white);
  color: var(--header);
  font-size: 16px;
  font-weight: 500;
  display: inline;
  padding-top: 35px;
  line-height: 1.2;
}
.project-image-items:hover .hover-button {
  opacity: 1;
  visibility: visible;
}
.project-image-items:hover::before {
  bottom: 0;
  visibility: visible;
  opacity: 1;
}
.project-image-items:hover .project-content {
  opacity: 1;
  visibility: visible;
  bottom: 40px;
}

.project-section .project-dot .swiper-pagination-bullet {
  width: 250px;
  height: 2px;
  border-radius: 0;
  background: #d4d4d4;
  opacity: 1;
  transition: 0.6s;
  margin: 0 !important;
}
@media (max-width: 1199px) {
  .project-section .project-dot .swiper-pagination-bullet {
    width: 80px;
  }
}
.project-section .project-dot .swiper-pagination-bullet-active {
  opacity: 1;
  background: var(--theme);
}

.project-image {
  position: relative;
  margin-top: 24px;
  height: 345px;
  overflow: hidden;
}
.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.project-image.style-2 {
  height: 715px;
}
.project-image.style-3 {
  height: 205px;
}
.project-image .portfolio-content {
  position: absolute;
  left: 15px;
  right: 15px;
  bottom: -100px;
  transition: all 0.4s ease-in-out;
  opacity: 0;
  visibility: hidden;
}
.project-image .portfolio-content h3 {
  background: #04D493;
  padding: 15px 15px;
  color: var(--white);
  line-height: 1;
  font-family: "Satoshi", sans-serif;
  font-size: 16px;
  max-width: 170px;
}
.project-image .portfolio-content h3 a {
  color: var(--white);
}
.project-image .portfolio-content h3:hover {
  background-color: var(--theme);
}
.project-image .portfolio-content h4 {
  padding: 7px 30px;
  line-height: 1;
  background: rgba(255, 255, 255, 0.3);
  color: var(--white);
  display: inline-block;
}
.project-image:hover .portfolio-content {
  bottom: 30px;
  opacity: 1;
  visibility: visible;
}

.project-details-wrapper .project-details-image {
  margin-bottom: 50px;
}
.project-details-wrapper .project-details-image img {
  width: 100%;
  height: 100%;
}
.project-details-wrapper .project-details-content h2 {
  font-weight: 500;
}
.project-details-wrapper .project-details-content h3 {
  font-size: 30px;
  font-weight: 500;
}
.project-details-wrapper .project-details-content .list {
  margin-top: 20px;
}
.project-details-wrapper .project-details-content .list li i {
  color: var(--theme);
  margin-right: 10px;
}
.project-details-wrapper .project-details-content .list li:not(:last-child) {
  margin-bottom: 15px;
}
.project-details-wrapper .project-sidebar-widget {
  padding: 40px 30px;
  background-color: var(--bg);
  margin-bottom: 30px;
}
.project-details-wrapper .project-sidebar-widget .wid-title {
  margin-bottom: 15px;
}
.project-details-wrapper .project-sidebar-widget .wid-title h3 {
  position: relative;
  padding-bottom: 15px;
  display: inline-block;
  font-size: 22px;
  font-weight: 600;
}
.project-details-wrapper .project-sidebar-widget .wid-title h3::before {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 2px;
  content: "";
  background-color: var(--theme);
}
.project-details-wrapper .project-sidebar-widget .case-content-box .case-infobox li {
  color: var(--header);
  font-weight: 600;
  padding: 20px 0;
}
.project-details-wrapper .project-sidebar-widget .case-content-box .case-infobox li:not(:last-child) {
  border-bottom: 1px solid #d7d7d7;
}
.project-details-wrapper .project-sidebar-widget .case-content-box .theme-btn {
  width: 100%;
  margin-top: 18px;
  text-align: center;
}
.project-details-wrapper .project-sidebar-widget .case-content-box .theme-btn i {
  margin-left: 0;
  margin-right: 10px;
}

.relative-project-items {
  margin-top: 30px;
}
.relative-project-items .thumb img {
  width: 100%;
  height: 100%;
}
.relative-project-items .content {
  margin-top: 20px;
}
.relative-project-items .content h3 {
  margin-bottom: 5px;
}

.project-box-items-3 .thumb {
  position: relative;
  overflow: hidden;
}
.project-box-items-3 .thumb img {
  width: 100%;
  height: 100%;
}
.project-box-items-3 .thumb .content {
  position: absolute;
  bottom: -100px;
  left: 40px;
  right: 40px;
  background-color: var(--white);
  padding: 18px 30px;
  transition: all 0.4s ease-in-out;
}
.project-box-items-3 .thumb .content .icon {
  width: 40px;
  height: 40px;
  display: inline-block;
  border-radius: 50%;
  line-height: 40px;
  text-align: center;
  background-color: var(--theme);
  color: var(--white);
  position: absolute;
  top: 50%;
  right: 30px;
  transform: translateY(-50%);
}
.project-box-items-3 .thumb .content .icon:hover {
  background-color: var(--header);
}
.project-box-items-3 .thumb .content p {
  margin-bottom: 5px;
}
.project-box-items-3 .thumb .content h3 a:hover {
  color: var(--theme);
}
.project-box-items-3:hover .thumb .content {
  visibility: visible;
  opacity: 1;
  bottom: 40px;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* Track */
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px var(--theme);
  border-radius: 5px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: var(--theme);
  border-radius: 10px;
}

.fix {
  overflow: hidden;
}

.ralt {
  position: relative;
}

.ml-100 {
  margin-left: 100px;
}

.video-pulse::after,
.video-pulse::before {
  position: absolute;
  content: "";
  width: 90px;
  height: 90px;
  border: 1px solid var(--white);
  opacity: 0.7;
  left: 0;
  top: 0;
  border-radius: 50%;
  -webkit-animation-duration: 2.5s;
  animation-duration: 2.5s;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
  -webkit-animation-name: video-animation;
  animation-name: video-animation;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}

.video-pulse::before {
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}

.ripple {
  position: relative;
}
.ripple::before, .ripple::after {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 60px;
  height: 60px;
  -webkit-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
  border-radius: 50%;
  box-shadow: 0 0 0 0 rgba(214, 17, 30, 0.5);
  -webkit-animation: rippleOne 3s infinite;
  animation: rippleOne 3s infinite;
}
.ripple::before {
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
  content: "";
  position: absolute;
  right: 0;
  bottom: 0;
}
.ripple::after {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
  content: "";
  position: absolute;
  right: 0;
  bottom: 0;
}

.swiper-dot {
  text-align: center;
}
.swiper-dot .swiper-pagination-bullet {
  width: 30px;
  height: 8px;
  transition: 0.6s;
  background-color: var(--header);
  opacity: 1;
  border-radius: 60px;
  position: relative;
}
.swiper-dot .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: var(--color-gradient-1);
  transition: 0.6s;
  position: relative;
  width: 51px;
}

.swiper-dot-2 .swiper-pagination-bullet {
  width: 20px;
  height: 20px;
  transition: 0.6s;
  background-color: var(--white);
  opacity: 1;
  border-radius: 50px;
  position: relative;
  border: 2px solid var(--white);
  display: inline-block;
  margin: 3px 0px !important;
}
.swiper-dot-2 .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: transparent;
  transition: 0.6s;
  position: relative;
}

.array-button {
  display: flex;
  align-items: center;
  gap: 10px;
}
.array-button .array-prev, .array-button .array-next {
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 50%;
}
.array-button .array-prev {
  background-color: var(--theme);
  color: var(--white);
  transition: all 0.4s ease-in-out;
}
.array-button .array-prev:hover {
  background-color: var(--header);
}
.array-button .array-next {
  background-color: var(--header);
  color: var(--white);
  transition: all 0.4s ease-in-out;
}
.array-button .array-next:hover {
  background-color: var(--theme);
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: -20px;
}

@media (max-width: 991px) {
  br {
    display: none;
  }
}

/* background */
.bg-cover {
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  background-position: center;
}

.nice-select {
  color: var(--text);
  background-color: transparent;
  font-size: 16px;
  width: unset;
  outline: none;
  padding: 18px 30px;
  border: none;
  border: 1px solid #E4E4E4;
  font-weight: 500;
  color: var(--text);
  transition: all 0.4s ease-in-out;
}
@media (max-width: 575px) {
  .nice-select {
    padding: 12px 20px;
  }
}
.nice-select span {
  color: var(--text);
  font-size: 16px;
  margin-bottom: 0 !important;
  font-weight: 500;
}
.nice-select:hover::after {
  border-bottom: 1px solid var(--text);
  border-right: 1px solid var(--text);
}

.nice-select option {
  color: var(--text);
}

.nice-select .current {
  margin-right: 12px;
}

.nice-select:after {
  right: 23px;
  border-bottom: 1px solid var(--text);
  border-right: 1px solid var(--text);
  width: 10px;
  height: 10px;
}

.nice-select.open .list {
  background: var(--bg);
  margin-top: 16px;
  width: 100%;
  text-transform: capitalize;
  color: var(--text);
}

.nice-select .option.selected.focus {
  background: var(--bg);
  outline: none;
  color: var(--theme);
  text-transform: capitalize;
  font-size: 16px;
}

.nice-select .option {
  border: none;
}

.nice-select .option:hover {
  background: transparent;
}

.head-color {
  color: var(--header);
}

.theme-color-1 {
  color: var(--theme) !important;
}

.border-none {
  border-top: none !important;
}

.mbm-10 {
  margin-bottom: -20px;
}

.margin-minus-10 {
  margin-top: -10px;
}

.ml-30 {
  margin-left: 30px;
}
@media (max-width: 1199px) {
  .ml-30 {
    margin-left: 0;
  }
}

.box-shadow {
  box-shadow: var(---box-shadow);
}

.mt-30 {
  margin-top: 30px;
}

.page-nav-wrap ul li {
  display: inline-block;
}
.page-nav-wrap ul li .page-numbers {
  display: inline-block;
  width: 60px;
  height: 60px;
  line-height: 60px;
  border-radius: 50%;
  background: #f6f6f6;
  font-weight: 700;
  transition: all 0.3s ease-in-out;
  margin: 0 2px;
}
.page-nav-wrap ul li .page-numbers.current {
  background-color: var(--theme);
  color: var(--white);
}
@media (max-width: 767px) {
  .page-nav-wrap ul li .page-numbers {
    margin-top: 10px;
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-size: 14px;
  }
}
.page-nav-wrap ul li .page-numbers i {
  margin-top: 2px;
}
.page-nav-wrap ul li .page-numbers:hover {
  background-color: var(--theme);
  color: var(--white);
}

.border-top-none {
  border-top: none !important;
}

.faq-wrapper .faq-image {
  height: 580px;
  position: relative;
}
@media (max-width: 767px) {
  .faq-wrapper .faq-image {
    height: 500px;
  }
}
@media (max-width: 575px) {
  .faq-wrapper .faq-image {
    height: 400px;
  }
}
.faq-wrapper .faq-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.faq-wrapper .faq-image .button-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
@media (max-width: 1199px) {
  .faq-wrapper .faq-image .button-text {
    display: none;
  }
}
.faq-wrapper .faq-image .button-text span {
  font-weight: 600;
  color: var(--white);
}
.faq-wrapper .faq-image .button-text .video-btn {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  text-align: center;
  line-height: 80px;
  background-color: var(--white);
  color: var(--theme);
  display: inline-block;
}
.faq-wrapper .faq-image .button-text .ripple::before, .faq-wrapper .faq-image .button-text .ripple::after {
  width: 80px;
  height: 80px;
  box-shadow: 0 0 0 0 rgba(112, 0, 254, 0.5);
}
.faq-wrapper .faq-content {
  margin-left: 40px;
}
@media (max-width: 1199px) {
  .faq-wrapper .faq-content {
    margin-left: 0;
  }
}
.faq-wrapper .faq-content .accordion-item {
  border: none !important;
  background-color: var(--white);
  box-shadow: var(---box-shadow);
}
.faq-wrapper .faq-content .accordion-item .accordion-header .accordion-button {
  font-weight: 600;
  color: var(--header);
  letter-spacing: -0.2px;
  border: none !important;
  border-radius: 0;
  box-shadow: none;
  background-color: var(--white);
  padding: 30px 30px 0;
  text-transform: capitalize;
  font-size: 20px;
}
@media (max-width: 1199px) {
  .faq-wrapper .faq-content .accordion-item .accordion-header .accordion-button {
    font-size: 20px;
  }
}
@media (max-width: 575px) {
  .faq-wrapper .faq-content .accordion-item .accordion-header .accordion-button {
    font-size: 18px;
    padding: 20px 20px 0;
  }
}
.faq-wrapper .faq-content .accordion-item .accordion-header .accordion-button span {
  margin-right: 20px;
}
.faq-wrapper .faq-content .accordion-item .accordion-header .accordion-button::after {
  content: "\f324";
  font-family: "Font Awesome 5 Pro";
  background: transparent;
  font-weight: 500;
  transition: all 0.3s ease-in-out !important;
  color: var(--header);
  font-size: 16px;
}
.faq-wrapper .faq-content .accordion-item .accordion-header .accordion-button:not(.collapsed)::after {
  content: "\f325";
  font-family: "Font Awesome 5 Pro";
  background: transparent;
  font-weight: 500;
  color: var(--theme);
}
.faq-wrapper .faq-content .accordion-item .accordion-header .accordion-button.collapsed {
  background-color: var(--white);
  box-shadow: var(---box-shadow);
  padding: 25px 30px;
  color: var(--header);
}
.faq-wrapper .faq-content .accordion-item .accordion-collapse {
  box-shadow: var(---box-shadow);
}
.faq-wrapper .faq-content .accordion-item .accordion-collapse .accordion-body {
  padding-left: 70px;
  padding-top: 15px;
  padding-right: 40px;
  color: var(--text);
  background-color: var(--white);
  padding-bottom: 28px;
}
@media (max-width: 767px) {
  .faq-wrapper .faq-content .accordion-item .accordion-collapse .accordion-body {
    padding-left: 60px;
  }
}
@media (max-width: 575px) {
  .faq-wrapper .faq-content .accordion-item .accordion-collapse .accordion-body {
    padding-left: 50px;
    padding-bottom: 20px;
  }
}
.faq-wrapper .faq-content.style-2 {
  margin-left: 0;
}
.faq-wrapper .faq-image-2 {
  position: relative;
}
.faq-wrapper .faq-image-2 .faq-img {
  max-width: 480px;
  position: relative;
}
.faq-wrapper .faq-image-2 .faq-img .video-btn {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  text-align: center;
  line-height: 80px;
  background-color: var(--white);
  color: var(--theme);
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9;
}
.faq-wrapper .faq-image-2 .faq-img .ripple::before, .faq-wrapper .faq-image-2 .faq-img .ripple::after {
  width: 80px;
  height: 80px;
  box-shadow: 0 0 0 0 rgba(112, 0, 254, 0.5);
}
.faq-wrapper .faq-image-2 .faq-img img {
  width: 100%;
  height: 100%;
}
.faq-wrapper .faq-image-2 .faq-img-2 {
  position: absolute;
  bottom: -22%;
  right: 0;
}
@media (max-width: 991px) {
  .faq-wrapper .faq-image-2 .faq-img-2 {
    bottom: 0;
  }
}
@media (max-width: 575px) {
  .faq-wrapper .faq-image-2 .faq-img-2 {
    max-width: 200px;
  }
  .faq-wrapper .faq-image-2 .faq-img-2 img {
    width: 100%;
    height: 100%;
  }
}

.pricing-single-items {
  margin-top: 30px;
  background-color: var(--white);
  box-shadow: var(---box-shadow);
  padding: 40px;
  border-radius: 10px;
  transition: all 0.4s ease-in-out;
}
.pricing-single-items .pricing-header .post-head {
  background-color: var(--theme);
  color: var(--white);
  font-size: 16px;
  font-weight: 500;
  display: inline-block;
  padding: 3px 10px;
  padding: 7px 14px;
  border-radius: 5px;
  line-height: 1;
  margin-bottom: 10px;
}
.pricing-single-items .pricing-header p {
  font-weight: 500;
}
.pricing-single-items .pricing-header h2 {
  margin-top: 20px;
  font-size: 42px;
}
.pricing-single-items .pricing-header h2 sub {
  font-size: 16px;
  font-weight: 500;
  margin-left: -5px;
  color: var(--text);
}
.pricing-single-items .price-list {
  margin-top: 30px;
  border-top: 1px solid var(--border);
  padding-top: 30px;
}
.pricing-single-items .price-list li {
  font-weight: 400;
  font-size: 18px;
}
.pricing-single-items .price-list li:not(:last-child) {
  margin-bottom: 15px;
}
.pricing-single-items .price-list li i {
  color: var(--theme);
  margin-right: 10px;
}
.pricing-single-items .pricing-button {
  margin-top: 40px;
}
.pricing-single-items .pricing-button .theme-btn {
  width: 100%;
  text-align: center;
}
@media (max-width: 1399px) {
  .pricing-single-items .pricing-button .theme-btn {
    padding: 20px 30px;
  }
}
.pricing-single-items:hover {
  transform: translateY(-10px);
}
.pricing-single-items.active {
  background-color: var(--theme);
}
.pricing-single-items.active .pricing-header .post-head {
  background-color: var(--white);
  color: var(--header);
}
.pricing-single-items.active .pricing-header p {
  color: var(--white);
}
.pricing-single-items.active .pricing-header h2 {
  color: var(--white);
}
.pricing-single-items.active .pricing-header h2 sub {
  color: var(--white);
}
.pricing-single-items.active .price-list {
  border-top: 1px solid var(--white);
}
.pricing-single-items.active .price-list li {
  color: var(--white);
}
.pricing-single-items.active .price-list li i {
  color: var(--white);
}
.pricing-single-items.active .pricing-button .theme-btn {
  width: 100%;
  text-align: center;
  background-color: var(--header);
}
.pricing-single-items.active .pricing-button .theme-btn::before, .pricing-single-items.active .pricing-button .theme-btn::after {
  background-color: var(--white);
}
.pricing-single-items.active .pricing-button .theme-btn:hover {
  color: var(--header);
}

.pricing-section {
  position: relative;
}
.pricing-section .pricing-shape {
  position: absolute;
  top: 10%;
  left: 5%;
}
@media (max-width: 1199px) {
  .pricing-section .pricing-shape {
    display: none;
  }
}

.cta-content .section-title {
  margin-bottom: 50px;
}
.cta-content .section-title h2 {
  font-size: 80px;
}
@media (max-width: 991px) {
  .cta-content .section-title h2 {
    font-size: 68px;
  }
}
@media (max-width: 767px) {
  .cta-content .section-title h2 {
    font-size: 52px;
  }
}
@media (max-width: 575px) {
  .cta-content .section-title h2 {
    font-size: 42px;
  }
}
.cta-content .section-title span {
  font-weight: 600;
  display: inline-block;
  text-transform: capitalize;
  font-size: 18px;
  margin-bottom: 15px;
}

.cta-contact-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 1199px) {
  .cta-contact-item {
    flex-wrap: wrap;
    gap: 30px;
    justify-content: center;
    text-align: center;
  }
}
.cta-contact-item h2 {
  color: var(--white);
}
.cta-contact-item .theme-btn {
  background-color: var(--white);
  color: var(--header);
}
.cta-contact-item .theme-btn:hover {
  color: var(--white);
}

.deal-wrapper .deal-content p {
  font-weight: 500;
}
.deal-wrapper .deal-content .coming-soon {
  margin-top: 40px;
  margin-bottom: 50px;
}
@media (max-width: 767px) {
  .deal-wrapper .deal-content .coming-soon {
    margin-bottom: 40px;
  }
}
@media (max-width: 575px) {
  .deal-wrapper .deal-content .coming-soon {
    margin-top: 30px;
    margin-bottom: 30px;
  }
}
.deal-wrapper .deal-content .coming-soon ul {
  display: flex;
  align-items: center;
  gap: 15px;
}
.deal-wrapper .deal-content .coming-soon ul li {
  font-weight: 500;
  text-align: center;
}
.deal-wrapper .deal-content .coming-soon ul li span {
  display: block;
  width: 70px;
  height: 70px;
  line-height: 70px;
  text-align: center;
  border-radius: 50%;
  background-color: var(--white);
  font-weight: 600;
  color: var(--header);
  margin-bottom: 5px;
  font-size: 22px;
}
.deal-wrapper .deal-image {
  position: relative;
}
.deal-wrapper .deal-image .thumb {
  position: relative;
  z-index: 3;
  text-align: center;
}
@media (max-width: 1199px) {
  .deal-wrapper .deal-image .thumb img {
    width: 100%;
    height: 100%;
  }
}
.deal-wrapper .deal-image .deal-shape {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}
@media (max-width: 1199px) {
  .deal-wrapper .deal-image .deal-shape {
    top: 50%;
    right: initial;
    left: 50%;
    transform: translate(-50%, -50%);
    bottom: initial;
  }
  .deal-wrapper .deal-image .deal-shape img {
    width: 100%;
    height: 100%;
  }
}

.cta-items-2 {
  padding: 80px 0;
  position: relative;
  z-index: 9;
  text-align: center;
}
@media (max-width: 1199px) {
  .cta-items-2 {
    padding: initial;
  }
}
.cta-items-2 .section-title .sub-title span {
  color: var(--white) !important;
  -webkit-text-fill-color: initial;
}
.cta-items-2 .section-title h2 {
  font-size: 72px;
}
@media (max-width: 991px) {
  .cta-items-2 .section-title h2 {
    font-size: 60px;
  }
}
@media (max-width: 767px) {
  .cta-items-2 .section-title h2 {
    font-size: 50px;
  }
}
@media (max-width: 575px) {
  .cta-items-2 .section-title h2 {
    font-size: 40px;
  }
}
.cta-items-2 .theme-btn {
  background-color: var(--white);
  color: var(--header);
}
.cta-items-2 .theme-btn:hover {
  color: var(--white);
}

.cta-section {
  position: relative;
}
.cta-section .cta-shape-1 {
  position: absolute;
  top: 10%;
  left: 0;
}
@media (max-width: 1600px) {
  .cta-section .cta-shape-1 {
    display: none;
  }
}
.cta-section .cta-shape-2 {
  position: absolute;
  bottom: 0;
  right: 0;
}
@media (max-width: 1600px) {
  .cta-section .cta-shape-2 {
    display: none;
  }
}
.cta-section .offer-shape {
  position: absolute;
  top: 0;
  left: 0;
}
@media (max-width: 1399px) {
  .cta-section .offer-shape {
    display: none;
  }
}
.cta-section .offer-shape-2 {
  position: absolute;
  right: 0;
  bottom: 0;
}
@media (max-width: 1399px) {
  .cta-section .offer-shape-2 {
    display: none;
  }
}

.cta-wrapper-2 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 100px 0je;
}
@media (max-width: 991px) {
  .cta-wrapper-2 {
    flex-wrap: wrap;
    justify-content: center;
    text-align: center;
    padding: 40px 0;
  }
}
.cta-wrapper-2 .content h2 {
  font-size: 60px;
  color: var(--white);
  margin-bottom: 20px;
}
@media (max-width: 767px) {
  .cta-wrapper-2 .content h2 {
    font-size: 52px;
  }
}
@media (max-width: 575px) {
  .cta-wrapper-2 .content h2 {
    font-size: 40px;
  }
}
.cta-wrapper-2 .content h3 {
  color: var(--white);
  font-size: 25px;
}
.cta-wrapper-2 .content .button-items {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 40px;
}
@media (max-width: 991px) {
  .cta-wrapper-2 .content .button-items {
    justify-content: center;
    flex-wrap: wrap;
  }
}
.cta-wrapper-2 .content .button-items .theme-btn {
  background-color: var(--white);
  color: var(--header);
}
.cta-wrapper-2 .content .button-items .theme-btn:hover {
  color: var(--white);
}
.cta-wrapper-2 .content .button-items .theme-btn.style-2 {
  background-color: var(--header);
  color: var(--white);
}
.cta-wrapper-2 .content .button-items .theme-btn.style-2::before, .cta-wrapper-2 .content .button-items .theme-btn.style-2::after {
  background-color: var(--white);
}
.cta-wrapper-2 .content .button-items .theme-btn.style-2:hover {
  color: var(--header);
}

.cta-section-3 {
  position: relative;
}
.cta-section-3::before {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  content: "";
  background-color: var(--bg);
  top: 50%;
}

.cta-wrapper-3 {
  padding: 80px;
  position: relative;
  margin-top: 65px;
}
@media (max-width: 1199px) {
  .cta-wrapper-3 {
    margin-top: 85px;
  }
}
@media (max-width: 991px) {
  .cta-wrapper-3 {
    text-align: center;
    margin-top: initial;
  }
}
.cta-wrapper-3 .cta-content h2 {
  color: var(--white);
  margin-bottom: 40px;
}
.cta-wrapper-3 .cta-content .theme-btn {
  background-color: var(--white);
  color: var(--header);
}
.cta-wrapper-3 .cta-content .theme-btn::before, .cta-wrapper-3 .cta-content .theme-btn::after {
  background-color: var(--header);
}
.cta-wrapper-3 .cta-content .theme-btn:hover {
  color: var(--white);
}
.cta-wrapper-3 .cta-shape {
  position: absolute;
  right: 80px;
  bottom: 0;
}
@media (max-width: 991px) {
  .cta-wrapper-3 .cta-shape {
    display: none;
  }
}

.instagram-banner-items .banner-image {
  position: relative;
}
@media (max-width: 767px) {
  .instagram-banner-items .banner-image {
    height: 450px;
  }
  .instagram-banner-items .banner-image img {
    object-fit: cover;
  }
}
@media (max-width: 575px) {
  .instagram-banner-items .banner-image {
    height: 380px;
  }
}
.instagram-banner-items .banner-image img {
  width: 100%;
  height: 100%;
}
.instagram-banner-items .banner-image::before {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  content: "";
  background: var(--black);
  opacity: 0.6;
  transform: scale(1, 0);
  transition: transform 500ms ease;
  transform-origin: bottom center;
  z-index: 1;
}
.instagram-banner-items .banner-image .icon {
  position: absolute;
  width: 68px;
  height: 68px;
  line-height: 68px;
  text-align: center;
  border-radius: 50%;
  background-color: var(--theme);
  font-size: 28px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 99;
  color: var(--white);
  opacity: 0;
  transition: all 0.4s ease-in-out;
}
.instagram-banner-items:hover .banner-image::before {
  transform: scale(1, 1);
  transform-origin: top center;
}
.instagram-banner-items:hover .banner-image .icon {
  opacity: 1;
}

.scrolling-wrap {
  overflow: hidden;
  white-space: nowrap;
  display: flex;
  gap: 20px;
}
.scrolling-wrap .comm {
  display: flex;
  align-items: center;
  animation: scroll 60s linear infinite;
  gap: 30px;
  cursor: pointer;
}
.scrolling-wrap .comm .cmn-textslide {
  font-size: 75px;
  font-weight: 700;
  line-height: 95px;
  text-transform: capitalize;
  color: var(--header);
  font-family: "Satoshi", sans-serif;
}
.scrolling-wrap .comm .cmn-textslide.color-2 {
  color: var(--theme);
}
.scrolling-wrap:hover .comm {
  animation-play-state: paused;
}
.scrolling-wrap.bg-style {
  background-color: var(--theme);
}
@media (max-width: 1399px) {
  .scrolling-wrap .comm {
    gap: 20px;
  }
  .scrolling-wrap .comm .cmn-textslide {
    font-size: 60px;
    line-height: 75px;
  }
  .scrolling-wrap .comm img {
    width: 70px;
  }
}
@media (max-width: 991px) {
  .scrolling-wrap {
    gap: 14px;
  }
  .scrolling-wrap .comm {
    gap: 14px;
  }
  .scrolling-wrap .comm .cmn-textslide {
    font-size: 48px;
    line-height: 60px;
  }
  .scrolling-wrap .comm img {
    width: 50px;
    object-fit: contain;
  }
}
@media (max-width: 575px) {
  .scrolling-wrap .comm {
    gap: 12px;
  }
  .scrolling-wrap .comm .cmn-textslide {
    font-size: 36px;
    line-height: 35px;
  }
  .scrolling-wrap .comm img {
    width: 50px;
    object-fit: contain;
  }
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}
@keyframes scroll2 {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-200%);
  }
}
.testimonial-wrapper {
  margin-right: -40%;
}
@media (max-width: 991px) {
  .testimonial-wrapper {
    margin-right: 0;
  }
}

.testimonial-box-items {
  margin-top: 30px;
  background-color: var(--white);
  padding: 30px;
}
.testimonial-box-items .star {
  color: #ecb332;
  margin-bottom: 15px;
}
.testimonial-box-items p {
  font-size: 18px;
  color: var(--header);
}
.testimonial-box-items .client-info {
  margin-top: 30px;
  display: flex;
  align-items: center;
  gap: 20px;
}
.testimonial-box-items .client-info .client-img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--bg);
}
.testimonial-box-items .client-info .content h4 {
  font-weight: 600;
  margin-bottom: 5px;
}
.testimonial-box-items.style-2 {
  padding: 50px 40px;
}
.testimonial-box-items.style-2 .client-info {
  margin-top: 0;
  justify-content: space-between;
  margin-bottom: 20px;
}
.testimonial-box-items.style-2 .client-info .client-item {
  display: flex;
  align-items: center;
  gap: 15px;
}
.testimonial-box-items.style-2 .star {
  margin-bottom: 0;
}

.testimonial-single-items {
  text-align: center;
  position: relative;
  z-index: 9;
}
.testimonial-single-items .testi-img {
  width: 160px;
  height: 160px;
  border-radius: 50%;
  background-color: var(--bg);
  margin: 0 auto;
}
.testimonial-single-items p {
  font-size: 28px;
  font-weight: 600;
  color: var(--header);
  line-height: 150%;
  margin-top: 30px;
}
@media (max-width: 1199px) {
  .testimonial-single-items p {
    font-size: 24px;
  }
}
@media (max-width: 991px) {
  .testimonial-single-items p {
    font-size: 22px;
  }
}
@media (max-width: 767px) {
  .testimonial-single-items p {
    font-size: 20px;
  }
}
@media (max-width: 575px) {
  .testimonial-single-items p {
    font-size: 18px;
    font-weight: 500;
  }
}
.testimonial-single-items .client-info {
  margin-top: 25px;
}
.testimonial-single-items .client-info h3 {
  font-size: 26px;
  font-weight: 600;
}
@media (max-width: 575px) {
  .testimonial-single-items .client-info h3 {
    font-size: 22px;
  }
}

.testimonial-section {
  position: relative;
}
.testimonial-section .shape-1 {
  position: absolute;
  bottom: 0;
  left: 0;
}
@media (max-width: 1199px) {
  .testimonial-section .shape-1 {
    display: none;
  }
}
.testimonial-section .shape-2 {
  position: absolute;
  right: 0;
  top: 0;
}
@media (max-width: 1199px) {
  .testimonial-section .shape-2 {
    display: none;
  }
}

.brand-wrapper {
  margin-top: -5px;
}
.brand-wrapper h6 {
  font-size: 22px;
  position: relative;
  margin-bottom: 50px;
  color: var(--header);
  font-weight: 700;
}
.brand-wrapper h6::before {
  position: absolute;
  top: 15px;
  left: 35px;
  content: "";
  height: 1px;
  width: 450px;
  background-color: var(--theme);
}
@media (max-width: 1399px) {
  .brand-wrapper h6::before {
    display: none;
  }
}
.brand-wrapper h6::after {
  position: absolute;
  top: 15px;
  right: 35px;
  content: "";
  height: 1px;
  width: 450px;
  background-color: var(--theme);
}
@media (max-width: 1399px) {
  .brand-wrapper h6::after {
    display: none;
  }
}

.news-box-items {
  margin-top: 30px;
  background-color: #F5F5F5;
  transition: all 0.4s ease-in-out;
  overflow: hidden;
}
.news-box-items .news-image {
  position: relative;
  z-index: 2;
  overflow: hidden;
}
.news-box-items .news-image img {
  width: 100%;
  height: 100%;
  position: relative;
  display: block;
  object-fit: cover;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.news-box-items .news-image img:first-child {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  -webkit-transform: translateX(50%) scaleX(2);
  transform: translateX(50%) scaleX(2);
  opacity: 0;
  -webkit-filter: blur(10px);
  filter: blur(10px);
}
.news-box-items .news-content {
  padding: 20px 30px;
}
.news-box-items .news-content .post-list {
  display: flex;
  align-items: center;
  gap: 95px;
  margin-bottom: 15px;
}
.news-box-items .news-content .post-list li {
  transition: all 0.4s ease-in-out;
}
.news-box-items .news-content h4 {
  margin-bottom: 30px;
  font-size: 24px;
}
.news-box-items .news-content .author-items {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #d1d3e0;
  padding-top: 30px;
  transition: all 0.4s ease-in-out;
}
.news-box-items .news-content .author-items .author-info {
  display: flex;
  align-items: center;
  gap: 14px;
}
.news-box-items .news-content .author-items .author-info .content h6 {
  font-size: 16px;
}
.news-box-items .news-content .author-items .author-info .content p {
  font-size: 14px;
}
.news-box-items .news-content .author-items .link-btn {
  color: var(--header);
}
.news-box-items:hover, .news-box-items.active {
  background-color: var(--theme);
}
.news-box-items:hover .news-image img:first-child, .news-box-items.active .news-image img:first-child {
  -webkit-transform: translateX(0) scaleX(1);
  transform: translateX(0) scaleX(1);
  opacity: 1;
  -webkit-filter: blur(0);
  filter: blur(0);
}
.news-box-items:hover .news-image img:nth-child(2), .news-box-items.active .news-image img:nth-child(2) {
  -webkit-transform: translateX(-50%) scaleX(2);
  transform: translateX(-50%) scaleX(2);
  opacity: 0;
  -webkit-filter: blur(10px);
  filter: blur(10px);
}
.news-box-items:hover .news-content .post-list li, .news-box-items.active .news-content .post-list li {
  color: var(--white);
}
.news-box-items:hover .news-content h4 a, .news-box-items.active .news-content h4 a {
  color: var(--white);
}
.news-box-items:hover .news-content .author-items, .news-box-items.active .news-content .author-items {
  border-top: 1px solid var(--white);
}
.news-box-items:hover .news-content .author-items .author-info .content h6, .news-box-items.active .news-content .author-items .author-info .content h6 {
  color: var(--white);
}
.news-box-items:hover .news-content .author-items .author-info .content p, .news-box-items.active .news-content .author-items .author-info .content p {
  color: var(--white);
}
.news-box-items:hover .news-content .link-btn, .news-box-items.active .news-content .link-btn {
  color: var(--white);
}

.news-card-items {
  margin-top: 30px;
  padding: 30px;
  border: 1px solid rgb(222, 222, 222);
  position: relative;
  transition: all 0.4s ease-in-out;
}
.news-card-items .news-thumb {
  position: relative;
  z-index: 9;
}
.news-card-items .news-thumb img {
  width: 100%;
  height: 100%;
}
.news-card-items .news-thumb .news-date {
  position: absolute;
  right: 0;
  bottom: -30px;
}
.news-card-items .news-thumb .news-date span {
  background: var(--theme);
  color: var(--white);
  width: 60px;
  height: 30px;
  display: block;
  font-size: 18px;
  font-weight: 700;
  text-align: center;
}
.news-card-items .news-thumb .news-date span:nth-child(2) {
  background-color: var(--header);
}
.news-card-items .news-content {
  margin-top: 20px;
  position: relative;
  z-index: 9;
}
.news-card-items .news-content .news-tag {
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
  gap: 10px;
}
.news-card-items .news-content .news-tag a {
  background: #f5f5f8;
  padding: 0px 12px;
  font-size: 14px;
  color: var(--text);
  font-weight: 500;
}
.news-card-items .news-content .news-tag a:hover {
  background: var(--theme);
  color: var(--white);
}
.news-card-items .news-content h3 {
  font-size: 26px;
}
@media (max-width: 1399px) {
  .news-card-items .news-content h3 {
    font-size: 22px;
  }
}
.news-card-items .news-content h3 a:hover {
  color: var(--theme);
}
.news-card-items .news-content .client-info {
  margin-top: 30px;
  display: flex;
  align-items: center;
  gap: 15px;
}
.news-card-items .news-content .client-info .client-img {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: var(--bg);
}
.news-card-items::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  border-top: 1px solid var(--theme);
  border-left: 1px solid var(--theme);
  transition: 0.5s;
  opacity: 0;
  visibility: hidden;
}
.news-card-items::after {
  position: absolute;
  content: "";
  bottom: 0;
  right: 0;
  width: 0;
  height: 0;
  border-bottom: 1px solid var(--theme);
  border-right: 1px solid var(--theme);
  transition: 0.5s;
  opacity: 0;
  visibility: hidden;
}
.news-card-items:hover {
  transform: translateY(-10px);
}
.news-card-items:hover::after {
  width: 100%;
  height: 100%;
  visibility: visible;
  opacity: 1;
}
.news-card-items:hover::before {
  width: 100%;
  height: 100%;
  visibility: visible;
  opacity: 1;
}

.single-news-style-1 {
  background-color: var(--white);
  margin-top: 30px;
  box-shadow: var(---box-shadow);
}
.single-news-style-1 .news-image {
  padding: 10px 10px 0px 10px;
  position: relative;
}
.single-news-style-1 .news-image img {
  width: 100%;
  height: 100%;
}
.single-news-style-1 .news-image .post-cat {
  position: absolute;
  bottom: 0;
  right: 8%;
}
.single-news-style-1 .news-image .post-cat .cat-name {
  color: var(--white);
  font-weight: 500;
  padding: 10px 24px;
  background-color: var(--theme);
  display: inline-block;
  transition: all 0.4s ease-in-out;
  font-size: 16px;
}
.single-news-style-1 .news-image .post-cat .cat-name:hover {
  background-color: var(--header);
}
@media (max-width: 575px) {
  .single-news-style-1 .news-image .post-cat .cat-name {
    font-size: 14px;
    padding: 10px 12px;
  }
}
.single-news-style-1 .news-content {
  padding: 30px 35px;
}
.single-news-style-1 .news-content .author-item {
  display: flex;
  align-items: center;
  gap: 30px;
}
@media (max-width: 1399px) {
  .single-news-style-1 .news-content .author-item {
    flex-wrap: wrap;
    gap: 15px;
  }
}
.single-news-style-1 .news-content .author-item ul li {
  font-size: 16px;
  font-weight: 500;
}
@media (max-width: 575px) {
  .single-news-style-1 .news-content .author-item ul li {
    font-size: 15px;
  }
}
.single-news-style-1 .news-content .author-item ul li i {
  margin-right: 10px;
}
.single-news-style-1 .news-content .author-item .post-author {
  gap: 12px;
}
.single-news-style-1 .news-content .author-item .post-author p {
  font-weight: 500;
}
.single-news-style-1 .news-content h3 {
  margin-bottom: 15px;
}
.single-news-style-1 .news-content h3 a {
  -webkit-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  transition: all 0.2s ease;
  margin: 0;
  text-transform: none;
  background-position: 0 95%;
  background-repeat: no-repeat;
  background-size: 0% 2px;
  display: inline;
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
}
.single-news-style-1 .news-content h3 a:hover {
  background-size: 100% 2px;
  background-image: linear-gradient(var(--theme), var(--theme));
  color: var(--theme);
}
@media (max-width: 575px) {
  .single-news-style-1 .news-content {
    padding: 25px 30px;
  }
}
.single-news-style-1 .news-content .link-btn {
  margin-top: 20px;
  display: inline-block;
}

.news-box-items-2 {
  margin-top: 30px;
  display: flex;
  align-items: center;
  gap: 30px;
}
@media (max-width: 1399px) {
  .news-box-items-2 {
    flex-wrap: wrap;
  }
}
.news-box-items-2 .news-image {
  max-width: 325px;
}
@media (max-width: 1399px) {
  .news-box-items-2 .news-image {
    max-width: 500px;
  }
}
.news-box-items-2 .news-image img {
  width: 100%;
  height: 100%;
}
.news-box-items-2 .news-content .post-cat {
  background: var(--color-gradient-1);
  text-align: center;
  color: var(--white);
  padding: 6px 8px;
  line-height: 1;
  text-transform: capitalize;
  font-size: 16px;
  display: inline-block;
  letter-spacing: 2px;
  margin-bottom: 15px;
}
.news-box-items-2 .news-content h3 {
  margin-bottom: 10px;
}
.news-box-items-2 .news-content h3 a:hover {
  color: var(--theme);
}
.news-box-items-2 .news-content p {
  margin-bottom: 10px;
}

.blog-wrapper h1, .blog-wrapper h2, .blog-wrapper h3, .blog-wrapper h4, .blog-wrapper h5, .blog-wrapper h6 {
  font-weight: 600;
}
.blog-wrapper .single-blog-post {
  margin-bottom: 40px;
  position: relative;
}
.blog-wrapper .single-blog-post .video__button {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}
.blog-wrapper .single-blog-post .video__button .video__btn__wrp {
  margin-bottom: 0;
}
.blog-wrapper .single-blog-post.post-details.format-quote .post-content {
  position: relative;
  background: transparent;
  border: none;
}
.blog-wrapper .single-blog-post.post-details .post-content {
  border: none;
}
.blog-wrapper .single-blog-post.post-details .post-content img {
  margin-top: 20px;
  margin-bottom: 20px;
}
.blog-wrapper .single-blog-post.post-details .post-content .single-post-image {
  width: 100%;
  height: 100%;
}
.blog-wrapper .single-blog-post.post-details .post-content h1 {
  font-size: 36px;
  line-height: 1.4;
}
.blog-wrapper .single-blog-post.post-details .post-content h1, .blog-wrapper .single-blog-post.post-details .post-content h2, .blog-wrapper .single-blog-post.post-details .post-content h3, .blog-wrapper .single-blog-post.post-details .post-content h4, .blog-wrapper .single-blog-post.post-details .post-content h5, .blog-wrapper .single-blog-post.post-details .post-content h6 {
  margin-top: 10px;
}
.blog-wrapper .single-blog-post.post-details .post-content table {
  margin-top: 20px;
}
@media (max-width: 767px) {
  .blog-wrapper .single-blog-post.post-details .post-content h1 {
    font-size: 28px;
  }
  .blog-wrapper .single-blog-post.post-details .post-content h3 {
    font-size: 20px;
  }
}
.blog-wrapper .single-blog-post.post-details blockquote, .blog-wrapper .single-blog-post.post-details .wp-block-quote {
  background-color: var(--theme);
  color: var(--white);
  font-size: 26px;
  line-height: 1.4;
  font-family: "Outfit", sans-serif;
  font-weight: 400;
  padding: 60px;
  text-align: center;
  margin: 40px 0px;
  position: relative;
  z-index: 1;
}
.blog-wrapper .single-blog-post.post-details blockquote a, .blog-wrapper .single-blog-post.post-details .wp-block-quote a {
  color: var(--white);
}
.blog-wrapper .single-blog-post.post-details blockquote a:hover, .blog-wrapper .single-blog-post.post-details .wp-block-quote a:hover {
  color: var(--text);
}
@media (max-width: 767px) {
  .blog-wrapper .single-blog-post.post-details blockquote, .blog-wrapper .single-blog-post.post-details .wp-block-quote {
    padding: 30px 15px;
    font-size: 18px;
    line-height: 1.5;
  }
}
.blog-wrapper .single-blog-post.post-details blockquote::before, .blog-wrapper .single-blog-post.post-details .wp-block-quote::before {
  right: 30px;
  font-size: 110px;
  line-height: 1;
  font-family: "Font Awesome 5 Pro";
  position: absolute;
  content: "\f10d";
  bottom: -20px;
  color: var(--white);
  z-index: -1;
  opacity: 0.1;
  font-weight: 900;
}
.blog-wrapper .single-blog-post.format-video .post-featured-thumb, .blog-wrapper .single-blog-post.category-video-post .post-featured-thumb {
  z-index: 1;
  position: relative;
}
.blog-wrapper .single-blog-post.format-video .post-featured-thumb::before, .blog-wrapper .single-blog-post.category-video-post .post-featured-thumb::before {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  content: "";
  background-color: #282835;
  opacity: 0.3;
  z-index: -1;
}
.blog-wrapper .single-blog-post.quote-post .post-content, .blog-wrapper .single-blog-post.format-quote .post-content {
  position: relative;
  background-color: var(--theme);
  border: none;
}
.blog-wrapper .single-blog-post.quote-post .post-content::before, .blog-wrapper .single-blog-post.format-quote .post-content::before {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  content: "";
  background-image: url(../../assets/img/quotepost.png);
  background-repeat: no-repeat;
  background-size: cover;
}
.blog-wrapper .single-blog-post.quote-post .quote-content, .blog-wrapper .single-blog-post.format-quote .quote-content {
  overflow: hidden;
  padding: 50px;
}
@media (max-width: 585px) {
  .blog-wrapper .single-blog-post.quote-post .quote-content, .blog-wrapper .single-blog-post.format-quote .quote-content {
    text-align: center;
    font-size: 24px;
    padding: 30px;
  }
}
.blog-wrapper .single-blog-post.quote-post .quote-content .icon, .blog-wrapper .single-blog-post.format-quote .quote-content .icon {
  font-size: 70px;
  line-height: 80px;
  float: left;
  overflow: hidden;
  margin-right: 30px;
}
@media (max-width: 585px) {
  .blog-wrapper .single-blog-post.quote-post .quote-content .icon, .blog-wrapper .single-blog-post.format-quote .quote-content .icon {
    float: none;
    margin-bottom: 10px;
    margin-right: 0;
    font-size: 50px;
    line-height: 60px;
  }
}
.blog-wrapper .single-blog-post.quote-post .quote-content .quote-text, .blog-wrapper .single-blog-post.format-quote .quote-content .quote-text {
  overflow: hidden;
}
.blog-wrapper .single-blog-post.quote-post .quote-content h2, .blog-wrapper .single-blog-post.format-quote .quote-content h2 {
  margin-top: -2px;
  font-size: 36px;
  color: var(--white);
}
.blog-wrapper .single-blog-post.quote-post .quote-content .post-meta, .blog-wrapper .single-blog-post.format-quote .quote-content .post-meta {
  margin-top: 10px;
}
.blog-wrapper .single-blog-post.quote-post .quote-content .post-meta span, .blog-wrapper .single-blog-post.quote-post .quote-content .post-meta i, .blog-wrapper .single-blog-post.format-quote .quote-content .post-meta span, .blog-wrapper .single-blog-post.format-quote .quote-content .post-meta i {
  color: var(--white);
}
.blog-wrapper .single-blog-post:last-child {
  margin-bottom: 0;
}
.blog-wrapper .single-blog-post .post-featured-thumb {
  height: 450px;
  width: 100%;
  position: relative;
  margin-bottom: -2px;
}
.blog-wrapper .single-blog-post .post-featured-thumb .post-date {
  position: absolute;
  top: 20px;
  left: 20px;
  padding: 15px 30px;
  background-color: var(--theme);
  text-align: center;
}
.blog-wrapper .single-blog-post .post-featured-thumb .post-date h4 {
  color: var(--white);
}
.blog-wrapper .single-blog-post .post-featured-thumb .post-date h4 span {
  color: var(--white);
  text-transform: uppercase;
  font-size: 15px;
}
@media (max-width: 991px) {
  .blog-wrapper .single-blog-post .post-featured-thumb {
    height: 280px;
  }
}
@media (max-width: 767px) {
  .blog-wrapper .single-blog-post .post-featured-thumb {
    height: 230px;
  }
}
.blog-wrapper .single-blog-post .post-featured-thumb .video-play-btn {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}
.blog-wrapper .single-blog-post .post-featured-thumb .video-play-btn .play-video {
  border-radius: 50%;
  width: 80px;
  height: 80px;
  line-height: 80px;
  font-size: 18px;
}
.blog-wrapper .single-blog-post .post-content {
  padding-top: 30px;
  overflow: hidden;
  box-sizing: border-box;
}
@media (max-width: 575px) {
  .blog-wrapper .single-blog-post .post-content {
    padding-top: 20px;
  }
}
.blog-wrapper .single-blog-post .post-content h2 {
  line-height: 1.4;
  font-size: 36px;
  font-weight: 600;
}
@media (max-width: 575px) {
  .blog-wrapper .single-blog-post .post-content h2 {
    font-size: 28px;
  }
}
.blog-wrapper .single-blog-post .post-content h2 a:hover {
  color: var(--theme);
}
.blog-wrapper .single-blog-post .post-content p {
  font-size: 16px;
}
@media (max-width: 767px) {
  .blog-wrapper .single-blog-post .post-content {
    padding: 30px 10px;
  }
}
@media (max-width: 414px) {
  .blog-wrapper .single-blog-post .post-content h2 {
    font-size: 22px;
  }
}
.blog-wrapper .single-blog-post .post-content .post-cat a {
  color: var(--white);
  display: inline-block;
  background-color: var(--theme);
  padding: 14px 18px 12px;
  line-height: 1;
  font-size: 16px;
  font-weight: 700;
  transition: all 0.3s ease-in-out;
  margin-bottom: 20px;
  text-transform: capitalize;
  font-family: "Inter", sans-serif;
}
@media (max-width: 767px) {
  .blog-wrapper .single-blog-post .post-content .post-cat a {
    font-size: 12px;
  }
}
.blog-wrapper .single-blog-post .post-content .post-cat a:hover {
  background-color: var(--header);
}
.blog-wrapper .single-blog-post .post-content ul {
  margin-bottom: 0;
  list-style-type: disc;
  margin-top: 30px;
}
.blog-wrapper .single-blog-post .post-content ul ol {
  margin-bottom: 0;
}
.blog-wrapper .single-blog-post .post-content ul ul {
  margin-bottom: 0;
}
.blog-wrapper .single-blog-post .post-content ul li {
  line-height: 1.5;
  margin-top: 10px;
  list-style-type: disc;
  color: var(--text);
  position: relative;
  font-size: 18px;
  padding-left: 30px;
}
.blog-wrapper .single-blog-post .post-content ul li::before {
  position: absolute;
  left: 0;
  top: 0;
  font-family: "Font Awesome 5 Pro";
  content: "\f00c";
  color: var(--theme);
}
.blog-wrapper .single-blog-post .post-content ol {
  margin-bottom: 0;
  list-style-type: decimal;
}
.blog-wrapper .single-blog-post .post-content ol ul {
  margin-bottom: 0;
}
.blog-wrapper .single-blog-post .post-content ol ol {
  margin-bottom: 0;
}
.blog-wrapper .single-blog-post .post-content ol li {
  line-height: 1.5;
  margin-top: 10px;
  list-style-type: decimal;
}
.blog-wrapper .single-blog-post .post-content p {
  margin-top: 15px;
}
.blog-wrapper .single-blog-post .post-content .post-meta {
  margin-bottom: 10px;
}
.blog-wrapper .single-blog-post .post-content .post-meta span {
  color: var(--text);
  font-weight: 500;
  font-size: 18px;
  line-height: 1;
  display: inline-block;
  margin-right: 25px;
}
@media (max-width: 767px) {
  .blog-wrapper .single-blog-post .post-content .post-meta span {
    font-size: 15px;
  }
}
.blog-wrapper .single-blog-post .post-content .post-meta span i {
  margin-right: 5px;
  color: var(--theme);
  font-weight: 700;
}
.blog-wrapper .single-blog-post .post-content .theme-btn i {
  transform: rotate(0);
}
.blog-wrapper .single-blog-post .post-content .author-info {
  overflow: hidden;
}
.blog-wrapper .single-blog-post .post-content .author-info .author-img {
  height: 40px;
  width: 40px;
  line-height: 40px;
  border-radius: 50%;
  background-color: #f2f2f2;
  margin-right: 15px;
  float: left;
  overflow: hidden;
  background-position: center;
  background-size: cover;
}
@media (max-width: 767px) {
  .blog-wrapper .single-blog-post .post-content .author-info .author-img {
    margin-right: 5px;
  }
}
.blog-wrapper .single-blog-post .post-content .author-info h5 {
  display: inline-block;
  line-height: 1;
  font-size: 16px;
  margin-top: 9px;
}
.blog-wrapper .single-blog-post .post-content .author-info h5 a:hover {
  color: var(--theme);
}
@media (max-width: 767px) {
  .blog-wrapper .single-blog-post .post-content .author-info h5 {
    font-size: 14px;
  }
}
.blog-wrapper .single-blog-post .post-content .post-link a {
  font-weight: 700;
}
.blog-wrapper .single-blog-post .post-content .post-link a:hover {
  color: var(--theme);
}
@media (max-width: 767px) {
  .blog-wrapper .single-blog-post .post-content .post-link a {
    font-size: 14px;
  }
}
.blog-wrapper .single-blog-post .post-content .post-link a i {
  margin-right: 5px;
}
@media (max-width: 767px) {
  .blog-wrapper .single-blog-post .post-content .post-link a i {
    margin-right: 3px;
  }
}

@media (min-width: 991px) {
  .news-area .main-sidebar {
    margin-left: 20px;
  }
}
@media (max-width: 991px) {
  .news-area .main-sidebar {
    margin-top: 40px;
  }
}
.news-area .main-sidebar .single-sidebar-widget {
  border: 2px solid #ededed;
  box-sizing: border-box;
  padding: 30px;
  margin-bottom: 40px;
}
.news-area .main-sidebar .single-sidebar-widget ul {
  padding-left: 0;
}
.news-area .main-sidebar .single-sidebar-widget ul > li {
  margin-bottom: 10px;
}
.news-area .main-sidebar .single-sidebar-widget ul > li a {
  font-size: 18px;
  font-weight: 600;
  text-transform: capitalize;
}
.news-area .main-sidebar .single-sidebar-widget ul > li a:hover {
  color: var(--theme);
}
.news-area .main-sidebar .single-sidebar-widget ul ul {
  padding-left: 20px;
}
.news-area .main-sidebar .single-sidebar-widget ul ul li {
  margin-top: 5px;
}
.news-area .main-sidebar .single-sidebar-widget:last-child {
  margin-bottom: 0;
}
.news-area .main-sidebar .single-sidebar-widget .wid-title {
  margin-bottom: 25px;
  padding-left: 45px;
  position: relative;
}
.news-area .main-sidebar .single-sidebar-widget .wid-title::before {
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  content: "";
  height: 4px;
  width: 18px;
  border-radius: 5px;
  position: absolute;
  background-color: var(--theme);
}
.news-area .main-sidebar .single-sidebar-widget .wid-title::after {
  left: 22px;
  top: 50%;
  transform: translateY(-50%);
  content: "";
  height: 4px;
  width: 4px;
  border-radius: 50%;
  position: absolute;
  background-color: var(--theme);
}
.news-area .main-sidebar .single-sidebar-widget .wid-title h4 {
  font-size: 24px;
  font-weight: 600;
}
.news-area .main-sidebar .single-sidebar-widget .social-link a {
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  background-color: var(--bg);
  font-size: 14px;
  margin-right: 5px;
  margin-bottom: 5px;
}
.news-area .main-sidebar .single-sidebar-widget .social-link a:hover {
  background-color: var(--theme);
  color: var(--white);
}

.search_widget form {
  width: 100%;
  position: relative;
}
.search_widget form input {
  background-color: var(--bg);
  font-size: 15px;
  padding: 20px;
  width: 100%;
  border: none;
  text-transform: capitalize;
  color: var(--header);
}
.search_widget form button {
  position: absolute;
  right: 0;
  top: 0;
  width: 70px;
  font-size: 18px;
  height: 100%;
  background-color: var(--theme);
  color: var(--white);
  text-align: center;
  transition: all 0.3s ease-in-out;
}
.search_widget form button:hover {
  background-color: var(--header);
}

.popular-posts .single-post-item, .popular_posts .single-post-item {
  overflow: hidden;
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid #f2f2f2;
}
.popular-posts .single-post-item:last-child, .popular_posts .single-post-item:last-child {
  margin-bottom: 0;
  border: none;
  padding-bottom: 0;
}
.popular-posts .single-post-item .thumb, .popular_posts .single-post-item .thumb {
  height: 95px;
  width: 95px;
  background-color: #f2f2f2;
  float: left;
  overflow: hidden;
  margin-right: 20px;
}
.popular-posts .single-post-item .post-content, .popular_posts .single-post-item .post-content {
  overflow: hidden;
}
.popular-posts .single-post-item .post-content h5, .popular_posts .single-post-item .post-content h5 {
  font-size: 18px;
  line-height: 22px;
  font-weight: 600;
  margin-bottom: 0;
  text-transform: capitalize;
}
.popular-posts .single-post-item .post-content h5 a:hover, .popular_posts .single-post-item .post-content h5 a:hover {
  color: var(--theme);
}
.popular-posts .single-post-item .post-content .post-date, .popular_posts .single-post-item .post-content .post-date {
  margin-top: 10px;
  color: var(--theme);
  font-weight: 600;
  font-size: 15px;
  text-transform: capitalize;
}
.popular-posts .single-post-item .post-content .post-date i, .popular_posts .single-post-item .post-content .post-date i {
  margin-right: 7px;
}

.widget_categories ul li {
  display: block;
}
.widget_categories ul li:last-child a {
  margin-bottom: 0;
}
.widget_categories ul li a {
  position: relative;
  background: var(--bg);
  padding: 17px 20px;
  line-height: 1;
  font-size: 14px;
  display: block;
  margin-bottom: 12px;
  font-weight: 500;
  transition: all 0.4s ease-in-out;
}
.widget_categories ul li a:hover {
  color: var(--theme);
}
.widget_categories ul li a i {
  margin-right: 10px;
}
.widget_categories ul li a span {
  position: absolute;
  width: 60px;
  line-height: 55px;
  height: 100%;
  content: "";
  background-color: var(--theme);
  right: 0;
  top: 0;
  text-align: center;
  color: var(--white);
}

.tagcloud a {
  display: inline-block;
  padding: 12px 22px;
  line-height: 1;
  font-weight: 600;
  background: var(--bg);
  margin-right: 10px;
  text-transform: capitalize;
  margin-bottom: 10px;
}
.tagcloud a:last-child {
  margin-right: 0;
}
.tagcloud a:hover {
  background-color: var(--theme);
  color: var(--white);
}

.widget_categories ul li {
  display: block;
  margin-bottom: 10px;
}

p.sticky-label {
  position: absolute;
  right: 0;
  background: var(--theme);
  color: var(--white);
  padding: 10px 40px;
  z-index: -1;
  top: 0;
  font-weight: 700;
}

.page-nav-wrap ul li {
  display: inline-block;
}
.page-nav-wrap ul li .page-numbers {
  display: inline-block;
  width: 60px;
  height: 60px;
  line-height: 60px;
  border-radius: 50%;
  background: #f6f6f6;
  font-weight: 700;
  transition: all 0.3s ease-in-out;
  margin: 0 2px;
}
.page-nav-wrap ul li .page-numbers.current {
  background-color: var(--theme);
  color: var(--white);
}
@media (max-width: 767px) {
  .page-nav-wrap ul li .page-numbers {
    margin-top: 10px;
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-size: 14px;
  }
}
.page-nav-wrap ul li .page-numbers i {
  margin-top: 2px;
}
.page-nav-wrap ul li .page-numbers:hover {
  background-color: var(--theme);
  color: var(--white);
}

.social-share a {
  margin-left: 10px;
  transition: all 0.4s ease-in-out;
}
.social-share a:hover {
  color: var(--theme);
}

.tag-share-wrap {
  padding-bottom: 50px;
}
.tag-share-wrap h4 {
  margin-bottom: 25px;
}
@media (max-width: 767px) {
  .tag-share-wrap h4 {
    margin-bottom: 10px;
    font-size: 18px;
  }
}
@media (max-width: 767px) {
  .tag-share-wrap .tagcloud {
    margin-bottom: 20px;
  }
}

.comments-section-wrap {
  overflow: hidden;
}

.comments-heading h3, .related-post-wrap h3, .comment-form-wrap h3 {
  font-size: 26px;
}
@media (max-width: 767px) {
  .comments-heading h3, .related-post-wrap h3, .comment-form-wrap h3 {
    font-size: 20px;
  }
}

.comments-item-list .single-comment-item {
  margin-top: 30px;
}
.comments-item-list .single-comment-item ul {
  margin-bottom: 15px;
}
.comments-item-list .single-comment-item p, .comments-item-list .single-comment-item span {
  font-size: 16px;
}
.comments-item-list .single-comment-item .author-img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  float: left;
  overflow: hidden;
  margin-right: 30px;
}
@media (max-width: 991px) {
  .comments-item-list .single-comment-item .author-img {
    width: 80px;
    height: 80px;
    margin-right: 20px;
  }
}
@media (max-width: 767px) {
  .comments-item-list .single-comment-item .author-img {
    width: 70px;
    height: 70px;
    margin-right: 15px;
  }
}
@media (max-width: 585px) {
  .comments-item-list .single-comment-item .author-img {
    float: none;
    margin-bottom: 20px;
  }
}
.comments-item-list .single-comment-item .author-info-comment {
  overflow: auto;
  padding-bottom: 25px;
  border-bottom: 1px solid #e2e2e2;
}
.comments-item-list .single-comment-item .author-info-comment .info {
  position: relative;
}
.comments-item-list .single-comment-item .author-info-comment .info .theme-btn {
  padding: 10px 18px;
  font-size: 14px;
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  color: var(--text);
  border: 1px solid #ebebeb;
  background: transparent;
  border-radius: 0;
  transition: all 0.4s ease-in-out;
  box-shadow: inherit;
}
@media (max-width: 767px) {
  .comments-item-list .single-comment-item .author-info-comment .info .theme-btn {
    font-size: 12px;
  }
}
.comments-item-list .single-comment-item .author-info-comment .info .theme-btn i {
  margin-right: 10px;
  margin-left: 0px !important;
  transition: all 0.4s ease-in-out;
  transform: rotate(0);
}
.comments-item-list .single-comment-item .author-info-comment .info .theme-btn.minimal-btn::before, .comments-item-list .single-comment-item .author-info-comment .info .theme-btn.minimal-btn::after {
  background-color: initial;
}
.comments-item-list .single-comment-item .author-info-comment .info .theme-btn:hover {
  background-color: var(--theme);
  color: var(--white);
}
.comments-item-list .single-comment-item .author-info-comment .info .theme-btn:hover a {
  color: var(--white) !important;
}
.comments-item-list .single-comment-item .author-info-comment .info .theme-btn:hover i {
  transform: translate(0);
}
.comments-item-list .single-comment-item .author-info-comment .info h5 {
  margin-bottom: 5px;
}
@media (max-width: 767px) {
  .comments-item-list .single-comment-item .author-info-comment .info h5 {
    font-size: 16px;
  }
}
.comments-item-list .single-comment-item .author-info-comment span {
  color: var(--theme);
  font-weight: 500;
}
.comments-item-list .single-comment-item .author-info-comment .theme-btn {
  padding: 7px 18px;
  font-size: 14px;
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
}
@media (max-width: 767px) {
  .comments-item-list .single-comment-item .author-info-comment .theme-btn {
    font-size: 12px;
  }
}
.comments-item-list .single-comment-item .author-info-comment .theme-btn i {
  margin-right: 5px;
}
.comments-item-list .single-comment-item .author-info-comment .theme-btn:hover a {
  color: var(--white);
}
.comments-item-list .single-comment-item .author-info-comment .comment-text {
  margin-top: 15px;
}

.comment-form {
  background-color: var(--white);
  width: 100%;
  margin-top: 30px;
}
@media (max-width: 767px) {
  .comment-form {
    padding: 30px;
  }
}
@media (max-width: 585px) {
  .comment-form {
    padding: 30px 15px;
  }
}
.comment-form label {
  margin-bottom: 4px;
}
.comment-form input, .comment-form textarea {
  margin-bottom: 20px;
  outline: none;
  padding: 20px 30px;
  line-height: 1;
  font-size: 16px;
  width: 100%;
  border: none;
  font-weight: 500;
  text-transform: capitalize;
  color: var(--header);
  background-color: var(--bg);
}
@media (max-width: 585px) {
  .comment-form input, .comment-form textarea {
    padding: 15px;
  }
}
.comment-form textarea {
  height: 200px;
  line-height: 1.3;
  border: none;
}
@media (max-width: 767px) {
  .comment-form textarea {
    height: 120px;
  }
}
.comment-form .theme-btn {
  display: inline-block;
  line-height: 1.2;
  padding: 24px 60px;
  transition: all 0.3s ease-in-out;
  margin-bottom: 0;
  margin-top: 20px;
}
@media (max-width: 767px) {
  .comment-form .theme-btn {
    padding: 20px 40px;
  }
}
@media (max-width: 585px) {
  .comment-form .theme-btn {
    padding: 15px 40px;
    font-size: 14px;
  }
}
.comment-form .theme-btn i {
  margin-right: 10px;
  margin-left: 0;
  transform: rotate(0);
}
.comment-form .theme-btn:hover i {
  transform: translate(0);
}

.comment ul {
  list-style-type: disc;
}
.comment ul ol {
  margin-bottom: 0;
}
.comment ul li {
  line-height: 1.5;
  margin-top: 10px;
  list-style-type: disc;
}
.comment ol ul {
  margin-bottom: 0;
}
.comment ol ol {
  margin-bottom: 0;
}
.comment ol li {
  line-height: 1.5;
  margin-top: 10px;
}

.site_info_widget .single-contact-info {
  overflow: auto;
  margin-bottom: 20px;
}
.site_info_widget .single-contact-info span {
  display: block;
  color: var(--theme);
}
.site_info_widget .single-contact-info .icon {
  width: 40px;
  height: 40px;
  line-height: 40px;
  color: var(--white);
  background-color: var(--theme);
  float: left;
  text-align: center;
  overflow: hidden;
  font-size: 16px;
  margin-right: 20px;
}
.site_info_widget .single-contact-info:nth-child(2n) .icon {
  background-color: var(--theme);
}
.site_info_widget .single-contact-info:nth-child(3n) .icon {
  background-color: var(--theme);
}
.site_info_widget .social-link {
  margin-top: 35px;
}
.site_info_widget .social-link a {
  margin-right: 8px !important;
}

.alignleft {
  float: left;
  margin-right: 1.5em;
  margin-bottom: 1.5em;
}

.contact-wrapper .contact-content .icon-items {
  background-color: var(--white);
  box-shadow: 0px 26px 81px 0px rgba(0, 0, 0, 0.09);
  padding: 30px 20px;
  text-align: center;
}
.contact-wrapper .contact-content .icon-items .icon {
  margin-bottom: 15px;
  padding: 0 15px;
}
.contact-wrapper .contact-items {
  margin-left: 30px;
  background-color: var(--white);
  box-shadow: var(---box-shadow);
  padding: 50px 40px;
}
@media (max-width: 1199px) {
  .contact-wrapper .contact-items {
    margin-left: 0;
  }
}
.contact-wrapper .contact-items .form-clt input, .contact-wrapper .contact-items .form-clt textarea {
  width: 100%;
  outline: none;
  border: none;
  background-color: #f3f3f3;
  padding: 16px 30px;
  color: var(--text);
  text-transform: capitalize;
}
.contact-wrapper .contact-items .form-clt textarea {
  padding-bottom: 150px;
}

.single-contact-card {
  background-color: #f8f8f8;
  padding: 40px;
  transition: all 0.3s ease-in-out;
}
@media (max-width: 585px) {
  .single-contact-card {
    padding: 30px;
  }
}
.single-contact-card:hover {
  background-color: var(--theme);
}
.single-contact-card:hover .icon {
  color: var(--theme) !important;
  background-color: var(--white) !important;
}
.single-contact-card:hover .bottom-part .icon {
  color: var(--theme) !important;
  background-color: var(--white) !important;
}
.single-contact-card:hover p,
.single-contact-card:hover span, .single-contact-card:hover h4 {
  color: #fff;
}
.single-contact-card .top-part {
  overflow: hidden;
}
.single-contact-card .top-part .icon {
  width: 70px;
  height: 70px;
  font-size: 20px;
  line-height: 70px;
  border-radius: 50%;
  background-color: #fff;
  text-align: center;
  float: left;
  color: var(--theme);
  overflow: hidden;
  margin-right: 20px;
  transition: all 0.4s ease-in-out;
}
.single-contact-card .top-part .title {
  overflow: hidden;
}
.single-contact-card .bottom-part {
  margin-top: 25px;
  overflow: hidden;
}
.single-contact-card .bottom-part .icon {
  width: 50px;
  height: 50px;
  font-size: 14px;
  line-height: 50px;
  border-radius: 50%;
  background-color: #fff;
  text-align: center;
  float: right;
  overflow: hidden;
  margin-left: 20px;
  transition: all 0.4s ease-in-out;
}
.single-contact-card .bottom-part .info {
  display: inline-block;
  overflow: hidden;
}

.contact-form-items {
  background-color: var(--bg);
  padding: 70px;
}
@media (max-width: 767px) {
  .contact-form-items {
    padding: 60px 40px;
  }
}
@media (max-width: 575px) {
  .contact-form-items {
    padding: 50px 30px;
  }
}
.contact-form-items .title {
  margin-bottom: 40px;
}
.contact-form-items .title h2 {
  font-size: 36px;
  margin-bottom: 5px;
}
.contact-form-items .form-clt input, .contact-form-items .form-clt textarea {
  padding: 18px 30px;
  border: none;
  outline: none;
  background-color: var(--white);
  width: 100%;
  font-size: 16px;
  font-weight: 500;
  color: var(--text);
}
@media (max-width: 575px) {
  .contact-form-items .form-clt input, .contact-form-items .form-clt textarea {
    padding: 12px 20px;
  }
}
.contact-form-items .form-clt textarea {
  padding: 18px 30px 110px;
}
@media (max-width: 575px) {
  .contact-form-items .form-clt textarea {
    padding: 12px 20px 90px;
  }
}
.contact-form-items .form-clt::placeholder {
  color: var(--text);
}

.office-google-map-wrapper iframe {
  width: 100%;
  height: 600px;
}

.footer-widgets-wrapper {
  padding: 90px 0 120px;
  position: relative;
  z-index: 9;
}
@media (max-width: 1199px) {
  .footer-widgets-wrapper {
    padding: 70px 0 100px;
  }
}
@media (max-width: 991px) {
  .footer-widgets-wrapper {
    padding: 50px 0 80px;
  }
}
.footer-widgets-wrapper .single-footer-widget {
  margin-top: 30px;
}
.footer-widgets-wrapper .single-footer-widget .widget-head {
  margin-bottom: 30px;
}
@media (max-width: 575px) {
  .footer-widgets-wrapper .single-footer-widget .widget-head {
    margin-bottom: 20px;
  }
}
.footer-widgets-wrapper .single-footer-widget .widget-head h3 {
  font-weight: bold;
  color: var(--header);
  font-size: 22px;
  display: inline-block;
}
.footer-widgets-wrapper .single-footer-widget .footer-content p {
  color: var(--header);
}
.footer-widgets-wrapper .single-footer-widget .footer-content .contact-info-area {
  margin-top: 25px;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .contact-info-area .contact-items {
  display: flex;
  gap: 20px;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .contact-info-area .contact-items:not(:last-child) {
  margin-bottom: 20px;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .contact-info-area .contact-items .icon {
  color: var(--theme);
  font-size: 22px;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .contact-info-area .contact-items .content h4 {
  color: var(--header);
  font-weight: 600;
  margin-top: 5px;
  font-size: 18px;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .contact-info-area .contact-items .content h4 a {
  color: var(--header);
}
.footer-widgets-wrapper .single-footer-widget .footer-content .contact-info-area .contact-items .content p {
  color: var(--header);
  font-weight: 500;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .contact-info-area .contact-items-2 {
  margin-top: 10px;
  display: inline-block;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .contact-info-area .contact-items-2 h4 {
  color: var(--header);
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 5px;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .contact-info-area .contact-items-2 p {
  color: var(--header);
  font-weight: 500;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .footer-input {
  position: relative;
  margin-top: 40px;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .footer-input input {
  background-color: var(--white);
  border: none;
  outline: none;
  padding: 16px 20px;
  width: 100%;
  color: var(--header);
}
.footer-widgets-wrapper .single-footer-widget .footer-content .footer-input input::placeholder {
  color: var(--header);
}
.footer-widgets-wrapper .single-footer-widget .footer-content .footer-input .newsletter-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 60px;
  height: 59px;
  line-height: 60px;
  text-align: center;
  background-color: var(--theme);
  color: var(--white);
}
.footer-widgets-wrapper .single-footer-widget .footer-content .play-app {
  margin-top: 30px;
  display: flex;
  align-items: center;
  gap: 10px;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .social-icon {
  gap: 10px;
  margin-top: 40px;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .social-icon a {
  background-color: var(--white);
  color: var(--header);
  width: 40px;
  height: 40px;
  text-align: center;
  line-height: 40px;
  border-radius: 50%;
  display: inline-block;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .social-icon a:hover {
  background-color: var(--theme);
  color: var(--white);
}
.footer-widgets-wrapper .single-footer-widget .footer-content .social-icon.style-2 {
  margin-top: 20px;
  gap: 15px;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .social-icon.style-2 a {
  width: initial;
  height: initial;
  background-color: transparent;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .social-icon.style-2 a:hover {
  color: var(--theme);
}
.footer-widgets-wrapper .single-footer-widget .footer-content .contact-list {
  margin-top: 30px;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .contact-list li {
  font-size: 18px;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .contact-list li:not(:last-child) {
  margin-bottom: 15px;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .contact-list li i {
  color: var(--theme);
  margin-right: 10px;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .contact-list li a {
  color: var(--header);
}
.footer-widgets-wrapper .single-footer-widget .footer-content .scan-items {
  display: flex;
  align-items: center;
  gap: 13px;
  flex-wrap: wrap;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .scan-items .scan-img {
  background-color: var(--white);
  border-radius: 10px;
  box-shadow: 0px 1px 17px 0px rgba(1, 16, 61, 0.09);
  display: inline-block;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .scan-items .store-list li {
  font-size: 14px;
  font-weight: 700;
  font-family: "Outfit", sans-serif;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .scan-items .store-list li:not(:last-child) {
  margin-bottom: 16px;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .scan-items .store-list li a {
  display: flex;
  align-items: center;
  color: var(--white);
  padding: 13px 35px;
  border-radius: 5px;
  background: #01103D;
  box-shadow: 0px 1px 2px 0px rgba(1, 16, 61, 0.14);
  gap: 10px;
  line-height: 1;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .scan-items .store-list li.active a {
  color: #01103D;
  background: var(--white);
}
.footer-widgets-wrapper .single-footer-widget .footer-content .brand-logo {
  margin-top: 30px;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .brand-logo img {
  max-width: 100%;
}
.footer-widgets-wrapper .single-footer-widget .list-items li {
  transition: all 0.4s ease-in-out;
  font-weight: 500;
}
.footer-widgets-wrapper .single-footer-widget .list-items li:not(:last-child) {
  margin-bottom: 15px;
}
.footer-widgets-wrapper .single-footer-widget .list-items li a {
  color: var(--header);
  font-weight: 500;
  font-size: 18px;
}
.footer-widgets-wrapper .single-footer-widget .list-items li:hover {
  margin-left: 5px;
}
.footer-widgets-wrapper .single-footer-widget .list-items li:hover a {
  color: var(--theme);
}
.footer-widgets-wrapper .single-footer-widget .footer-gallery .gallery-wrap .gallery-item {
  display: flex;
  align-items: center;
  gap: 10px;
}
.footer-widgets-wrapper .single-footer-widget .footer-gallery .gallery-wrap .gallery-item:not(:last-child) {
  margin-bottom: 10px;
}
.footer-widgets-wrapper .single-footer-widget .footer-gallery .gallery-wrap .gallery-item .thumb {
  position: relative;
}
@media (max-width: 575px) {
  .footer-widgets-wrapper .single-footer-widget .footer-gallery .gallery-wrap .gallery-item .thumb {
    width: 100px;
  }
}
.footer-widgets-wrapper .single-footer-widget .footer-gallery .gallery-wrap .gallery-item .thumb img {
  width: 100%;
  height: 100%;
}
.footer-widgets-wrapper .single-footer-widget .footer-gallery .gallery-wrap .gallery-item .thumb .icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  text-align: center;
  transition: 0.3s;
  opacity: 0;
  width: 100%;
  height: 100%;
}
.footer-widgets-wrapper .single-footer-widget .footer-gallery .gallery-wrap .gallery-item .thumb .icon::after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(112, 0, 254, 0.7);
  transition: 0.4s;
  opacity: 0;
  width: 100%;
  height: 100%;
}
.footer-widgets-wrapper .single-footer-widget .footer-gallery .gallery-wrap .gallery-item .thumb .icon i {
  color: var(--white);
  font-size: 22px;
  z-index: 99;
  position: relative;
  margin-top: 30px;
}
.footer-widgets-wrapper .single-footer-widget .footer-gallery .gallery-wrap .gallery-item .thumb:hover .icon {
  opacity: 1;
}
.footer-widgets-wrapper .single-footer-widget .footer-gallery .gallery-wrap .gallery-item .thumb:hover .icon::after {
  opacity: 1;
}
.footer-widgets-wrapper .single-footer-widget .contact-list-2 li {
  display: flex;
  align-items: center;
  gap: 15px;
}
.footer-widgets-wrapper .single-footer-widget .contact-list-2 li:not(:last-child) {
  margin-bottom: 30px;
}
.footer-widgets-wrapper .single-footer-widget .contact-list-2 li .icon {
  width: 42px;
  height: 42px;
  line-height: 38px;
  text-align: center;
  border-radius: 50%;
  background-color: var(--theme);
}
.footer-widgets-wrapper .single-footer-widget .contact-list-2 li .content span {
  color: #687799;
  font-weight: 500;
}
.footer-widgets-wrapper .single-footer-widget .contact-list-2 li .content h3 {
  font-weight: 500;
  font-size: 15px;
}

.footer-bottom {
  background-color: rgba(44, 63, 70, 0.0509803922);
  padding: 30px 0;
}
.footer-bottom .footer-bottom-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 767px) {
  .footer-bottom .footer-bottom-wrapper {
    flex-wrap: wrap;
    gap: 30px;
    text-align: center;
    justify-content: center;
  }
}
.footer-bottom .footer-bottom-wrapper p {
  color: var(--header);
  text-transform: capitalize;
  font-size: 18px;
}/*# sourceMappingURL=main.css.map */