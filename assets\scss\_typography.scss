/* --------------------------------------------
    Template De<PERSON><PERSON> & <PERSON>onts Styles
 ---------------------------------------------- */
@import url('https://fonts.googleapis.com/css2?family=Outfit:wght@100..900&display=swap');
@import url('https://fonts.cdnfonts.com/css/satoshi'); 

$heading-font: "Outfit", sans-serif;
$body-font: "Satoshi", sans-serif;

//font-family: "Font Awesome 5 Pro";
$fa: "Font Awesome 5 Pro";

body {
    font-family: $body-font;
    font-size: 16px;
    font-weight: normal;
    line-height: 28px;
    color: $text-color;
    background-color: $white;
	padding: 0;
	margin: 0;
	overflow-x: hidden;
}

ul {
	padding: 0;
	margin: 0;
	list-style: none;
}

button {
    border: none;
    background-color: transparent;
	padding: 0;
}

input:focus{
	color: var(--white);
	outline: none;
}

input{
	color: $white;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: $heading-font;
    margin: 0px;
	padding: 0;
    color: $header-color;
	text-transform: initial;
	@include transition;
}

h1 {
	font-size: 70px;
	font-weight: 700;
	line-height: 115%;
}

h2 {
	font-size: 50px;
	line-height: 110%;
	font-weight: 700;

	@include breakpoint(max-xl){
		font-size: 42px;
	}

	@include breakpoint(max-md){
		font-size: 44px;
	}

	@include breakpoint(max-sm){
		font-size: 38px;
	}
}
h3 {
	font-size: 22px;
	font-weight: 700;

	@include breakpoint(max-sm){
		font-size: 20px;
	}
}

h4 {
	font-size: 20px;
	font-weight: 700;
	line-height: 130%;
}

h5 {
	font-size: 18px;
	font-weight: 700;
}

h6 {
	font-size: 16px;
	font-weight: 700;
}

a {
    text-decoration: none;
    outline: none !important;
    cursor: pointer;
    color: $header-color ;
	@include transition;
}

p {
    margin: 0px;
	@include transition;
}



