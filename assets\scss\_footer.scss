.footer-widgets-wrapper {
    padding: 90px 0 120px;
    position: relative;
    z-index: 9;

    @include breakpoint(max-xl){
        padding: 70px 0 100px;
    }

    @include breakpoint(max-lg){
        padding: 50px 0 80px;
    }

    .single-footer-widget {
        margin-top: 30px;

        .widget-head {
            margin-bottom: 30px;

            @include breakpoint (max-sm){
               margin-bottom: 20px;
            }

            h3 {
                font-weight: bold;
                color: $header-color;
                font-size: 22px;
                display: inline-block;
            }
        }

        .footer-content {
            p {
                color: $header-color;
            }

            .contact-info-area {
                margin-top: 25px;

                .contact-items {
                    display: flex;
                    gap: 20px;

                    &:not(:last-child){
                        margin-bottom: 20px;
                    }

                    .icon {
                       color: $theme-color;
                       font-size: 22px;
                    }

                    .content {

                        h4 {
                            color: $header-color;
                            font-weight: 600;
                            margin-top: 5px;
                            font-size: 18px;

                            a {
                                color: $header-color;
                            }
                        }

                        p {
                            color: $header-color;
                            font-weight: 500;
                        }
                    }
                }

                .contact-items-2 {
                    margin-top: 10px;
                    display: inline-block;

                    h4 {
                        color: $header-color;
                        font-weight: 600;
                        font-size: 18px;
                        margin-bottom: 5px;
                    }

                    p {
                        color: $header-color;
                        font-weight: 500;
                    }
                }
            }

            .footer-input {
                position: relative;
                margin-top: 40px;

                input {
                    background-color: $white;
                    border: none;
                    outline: none;
                    padding: 16px 20px;
                    width: 100%;
                    color: $header-color;

                    &::placeholder {
                        color: $header-color;
                    }
                }

                .newsletter-btn {
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 60px;
                    height: 59px;
                    line-height: 60px;
                    text-align: center;
                    background-color: $theme-color;
                    color: $white;
                }
            }

            .play-app {
                margin-top: 30px;
                @include flex;
                gap: 10px;
            }

            .social-icon {
                gap: 10px;
                margin-top: 40px;

                a {
                    background-color: $white;                
                    color: $header-color;
                    width: 40px;
                    height: 40px;
                    text-align: center;
                    line-height: 40px;
                    border-radius: 50%;
                    display: inline-block;

                    &:hover {
                        background-color: $theme-color;
                        color: $white;
                    }
                }

                &.style-2 {
                    margin-top: 20px;
                    gap: 15px;

                    a {
                        width: initial;
                        height: initial;
                        background-color: transparent;

                        &:hover {
                            color: $theme-color;
                        }
                    }
                }
            }

            .contact-list {
                margin-top: 30px;

                li {
                    font-size: 18px;
                   

                    &:not(:last-child){
                        margin-bottom: 15px;
                    }

                    i {
                        color: $theme-color;
                        margin-right: 10px;
                    }

                    a {
                        color: $header-color;
                    }
                }
            }

            .scan-items {
                @include flex;
                gap: 13px;
                flex-wrap: wrap;

                .scan-img {
                    background-color: $white;
                    border-radius: 10px;
                    box-shadow: 0px 1px 17px 0px rgba(1, 16, 61, 0.09);
                    display: inline-block;
                    width: 100px;
                    height: 100px;
                    line-height: 100px;
                    text-align: center;
                }

                .store-list {
                    
                    li {
                        font-size: 14px;
                        font-weight: 700;
                        font-family: $heading-font;

                        &:not(:last-child){
                            margin-bottom: 16px;
                        }
                       
                        a {
                            @include flex;
                            color: $white;
                            padding: 13px 35px;
                            border-radius: 5px;
                            background: #01103D;
                            box-shadow: 0px 1px 2px 0px rgba(1, 16, 61, 0.14);
                            gap: 10px;
                            line-height: 1;
                        }

                        &.active {
                            a {
                                color: #01103D;
                                background: $white;
                            }
                        }
                    }
                }
           }

           .brand-logo {
                margin-top: 30px;

                img {
                    max-width: 100%;
                }
           }
        }

       .list-items {
            li {
                @include transition;
                font-weight: 500;

                &:not(:last-child){
                    margin-bottom: 15px;
                }

                a {
                    color: $header-color;
                    font-weight: 500;
                    font-size: 18px;
                }

                &:hover {
                    margin-left: 5px;
                    a {
                        color: $theme-color;
                    }
                }
            }
       }

       .footer-gallery {
            .gallery-wrap {
                .gallery-item {
                    @include flex;
                    gap: 10px;

                    &:not(:last-child){
                        margin-bottom: 10px;
                    }

                    .thumb {
                        position: relative;

                        @include breakpoint(max-sm){
                            width: 100px;
                        }

                        img {
                            @include imgw;
                        }

                        .icon {
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            z-index: 1;
                            text-align: center;
                            transition: .3s;
                            opacity: 0;
                            width: 100%;
                            height: 100%;

                            &::after{
                                position: absolute;
                                content: "";
                                top: 0;
                                left: 0;
                                right: 0;
                                bottom: 0;
                                background-color:rgba(112, 0, 254, 0.7);
                                transition: .4s;
                                opacity: 0;
                                width: 100%;
                                height: 100%;
                            }

                            i {
                                color: $white;
                                font-size: 22px;
                                z-index: 99;
                                position: relative;
                                margin-top: 30px;
                            }
                        }

                        &:hover {
                            .icon {
                                opacity: 1;
                                &::after {
                                    opacity: 1;
                                }
                            }
                        }
                    }
                }
            }
        }

        .contact-list-2 {
            li {
                @include flex;
                gap: 15px;

                &:not(:last-child){
                    margin-bottom: 30px;
                }

                .icon {
                    width: 42px;
                    height: 42px;
                    line-height: 38px;
                    text-align: center;
                    border-radius: 50%;
                    background-color: $theme-color;
                }

                .content {
                    span {
                        color: #687799;
                        font-weight: 500;
                    }
                    h3 {
                        font-weight: 500;
                        font-size: 20px;
                    }
                }
            }
        }
    }
}

.footer-bottom {
    background-color: #2C3F460D;
    padding: 30px 0;

    .footer-bottom-wrapper {
        @include flex;
        justify-content: space-between;

        @include breakpoint (max-md){
            flex-wrap: wrap;
            gap: 30px;
            text-align: center;
            justify-content: center;
        }

        p {
            color: $header-color;
            text-transform: capitalize;
            font-size: 18px;
        }
    }

}