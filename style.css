/*
Theme Name: Printnow
Author: ModinaTheme
Author URI: https://themeforest.net/user/modinatheme/
Description: Printing Company & Design Services HTML Template
Version: 1.1.0
*/

/*
=================================
|***    Table of contents:   ***|
=================================

1. General styles
2. Typography
3. Helpers
4. Preloader
5. Go up button
6. Header and navigation
7. Hero Section
8. About us
9. Services
10. Fun facts
11. Projects
12. Pricing
13. Testimonial
14. CTA
15. Team
16. Video Banner
17. Shop
18. Marquee
19. Blog
20. Footer

Main Style file-> assets/css/main.css
*/

/* Custom Software Card Enhancements */
.shop-items {
    position: relative;
    transition: all 0.3s ease-in-out;
}

.shop-items .shop-image {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
}

.shop-items .shop-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all 0.3s ease-in-out;
    border-radius: 8px;
}

/* Link Type Indicator */
.link-type-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
    z-index: 10;
}

.link-type-badge {
    background: linear-gradient(135deg, #7000fe, #AE34E8);
    color: white;
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(112, 0, 254, 0.3);
    white-space: nowrap;
}

/* Hover Effects */
.shop-items:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.shop-items:hover .shop-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(112, 0, 254, 0.8);
    z-index: 5;
    opacity: 1;
    transition: all 0.3s ease-in-out;
}

.shop-items:hover .link-type-indicator {
    opacity: 1;
    visibility: visible;
}

/* Different link type colors */
.shop-items[data-link-type="Demo"] .link-type-badge {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.shop-items[data-link-type="GitHub"] .link-type-badge {
    background: linear-gradient(135deg, #333, #6c757d);
}

.shop-items[data-link-type="Download"] .link-type-badge {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.shop-items[data-link-type="API"] .link-type-badge {
    background: linear-gradient(135deg, #fd7e14, #e55a4e);
}

.shop-items[data-link-type="Template"] .link-type-badge {
    background: linear-gradient(135deg, #8e2de2, #4a00e0);
}

/* Enhanced product icons */
.shop-items .product-icon li a {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease-in-out;
}

.shop-items .product-icon li:hover {
    transform: scale(1.1);
}

/* Demo Card Styles */
.demo-card {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.demo-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.demo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.demo-card:hover .demo-overlay {
    opacity: 1;
}

.demo-play-btn {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 10px;
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.demo-card:hover .demo-play-btn {
    transform: scale(1);
}

.demo-play-btn i {
    color: white;
    font-size: 20px;
    margin-left: 3px;
}

.demo-text {
    color: white;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.demo-description {
    color: #666;
    font-size: 13px;
    margin: 5px 0 0 0;
    line-height: 1.4;
}

/* Live Demo Card Styles */
.demo-card-live {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.demo-card-live:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.demo-iframe-container {
    position: relative;
    width: 100%;
    height: 250px;
    overflow: hidden;
    border-radius: 8px;
}

.demo-preview-iframe {
    width: 333.33%;
    height: 833px;
    border: none;
    transform: scale(0.3);
    transform-origin: 0 0;
    pointer-events: none;
    border-radius: 8px;
}

.demo-overlay-live {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.demo-card-live:hover .demo-overlay-live {
    opacity: 1;
}

.demo-overlay-live .demo-play-btn {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 10px;
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.demo-card-live:hover .demo-overlay-live .demo-play-btn {
    transform: scale(1);
}

.demo-overlay-live .demo-play-btn i {
    color: white;
    font-size: 18px;
}

.demo-overlay-live .demo-text {
    color: white;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .link-type-badge {
        font-size: 10px;
        padding: 6px 12px;
    }

    .shop-items:hover {
        transform: translateY(-3px);
    }

    .demo-play-btn {
        width: 50px;
        height: 50px;
    }

    .demo-play-btn i {
        font-size: 16px;
    }

    .demo-text {
        font-size: 12px;
    }
}